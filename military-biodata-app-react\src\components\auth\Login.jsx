import React, { useState } from "react";
import "./login.css";
import MedBlog from '../MedBlog.jsx';
import AboutUs from '../AboutUs.jsx';

// Demo personnel data for testing
const demoPersonnel = [
  {
    id: 1,
    email: "<EMAIL>",
    name: "<PERSON>",
    rank: "Lieutenant",
    serviceNumber: "NN/0123/A",
    service: "Navy",
    command: "Logistics Command",
    role: "personnel",
    avatar: "/images/logos/HQLC.png"
  },
  {
    id: 2,
    email: "<EMAIL>",
    name: "Dr. <PERSON>",
    rank: "Commander",
    serviceNumber: "NN/0456/M",
    service: "Navy",
    command: "Logistics Command",
    role: "doctor",
    avatar: "/images/logos/HQLC.png"
  },
  {
    id: 3,
    email: "<EMAIL>",
    name: "<PERSON>",
    rank: "Lieutenant",
    serviceNumber: "NN/0789/N",
    service: "Navy",
    command: "Logistics Command",
    role: "nurse",
    avatar: "/images/logos/HQLC.png"
  },
  {
    id: 4,
    email: "<EMAIL>",
    name: "Guest User",
    rank: "Civilian",
    serviceNumber: "GUEST-2024-00001",
    service: "Civilian",
    command: "Logistics Command",
    role: "guest",
    avatar: "/images/logos/HQLC.png"
  },
  {
    id: 5,
    email: "<EMAIL>",
    name: "RAdm OO FADEYI",
    rank: "Rear Admiral",
    serviceNumber: "NN/FOC/2024",
    service: "Navy",
    command: "Logistics Command",
    role: "officer",
    avatar: "/images/logos/HQLC.png"
  }
];

const Login = ({ onLoginSuccess, onSignUpClick }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const validateEmail = (email) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!email) {
      setError("❌ Email is required");
      return;
    }
    
    if (!validateEmail(email)) {
      setError("❌ Please enter a valid email address");
      return;
    }
    
    if (!password) {
      setError("❌ Password is required");
      return;
    }
    
    setLoading(true);
    setError("");
    
    try {
      // In a real app, you would make an API call to your backend
      // For demo purposes, we'll simulate a login with the demo data
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find user by email (case insensitive)
      const user = demoPersonnel.find(
        p => p.email.toLowerCase() === email.toLowerCase()
      );
      
      if (!user) {
        throw new Error("User not found");
      }
      
      // In a real app, you would verify the password here
      // For demo purposes, any password will work
      
      // Simulate successful login
      localStorage.setItem("token", "demo-token-123");
      localStorage.setItem("personnelId", user.id);
      localStorage.setItem("personnelName", user.name);
      localStorage.setItem("personnelRank", user.rank);
      localStorage.setItem("personnelBranch", user.service);
      localStorage.setItem("personnelRole", user.role);
      localStorage.setItem("personnelAvatar", user.avatar);
      localStorage.setItem("personnelServiceNumber", user.serviceNumber);
      
      // Call the onLoginSuccess callback from parent
      if (onLoginSuccess) {
        onLoginSuccess(user);
      }
    } catch (err) {
      setError("❌ Login failed. Please check your credentials.");
    } finally {
      setLoading(false);
    }
  };
  
  // State for guest view
  const [guestView, setGuestView] = useState(null);
  
  const handleBlogClick = () => {
    setGuestView('medblog');
  };
  
  const handleAboutUsClick = () => {
    setGuestView('about');
  };
  
  const handleBackToLogin = () => {
    setGuestView(null);
  };
  
  // Render based on the guest view
  if (guestView === 'medblog') {
    return (
      <div className="login-container">
        <div className="login-nav">
          <button className="nav-button" onClick={handleBackToLogin}>Back to Login</button>
        </div>
        <div className="guest-view-container">
          <React.Suspense fallback={<div>Loading...</div>}>
            <MedBlog />
          </React.Suspense>
        </div>
      </div>
    );
  }
  
  if (guestView === 'about') {
    return (
      <div className="login-container">
        <div className="login-nav">
          <button className="nav-button" onClick={handleBackToLogin}>Back to Login</button>
        </div>
        <div className="guest-view-container">
          <React.Suspense fallback={<div>Loading...</div>}>
            <AboutUs />
          </React.Suspense>
        </div>
      </div>
    );
  }
  
  // Default login view
  return (
    <div className="login-container">
      <div className="login-nav">
        <button className="nav-button" onClick={handleBlogClick}>Medical Blog</button>
        <button className="nav-button" onClick={handleAboutUsClick}>About Us</button>
      </div>

      <div className="login-header">
        <img src="/images/logos/HQLC.png" alt="Logistics Command Logo" className="login-logo" />
        <h1>Logistics Command Medical Portal</h1>
        <p>Welcome to the Nigerian Navy Logistics Command Medical Portal. Access your comprehensive medical records, schedule appointments, track medications, and manage your health information securely in one place.</p>
      </div>

      <form onSubmit={handleSubmit} className="login-form">
        <h2 className="login-title">Personnel Login</h2>
        <p className="login-subtitle">Enter your credentials to access your secure medical portal</p>
        {error && <p className="login-error">{error}</p>}

        <label>Email Address</label>
        <input
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />

        <label>Password</label>
        <input
          type="password"
          placeholder="Enter your password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />

        <div className="form-footer">
          <p className="password-hint">For demo purposes, any password will work</p>
          <button type="submit" className="login-button" disabled={loading}>
            {loading ? "Logging in..." : "Login"}
          </button>
        </div>

        <div className="signup-link">
          <p>Don't have an account? <button onClick={onSignUpClick} className="signup-link-button">Register Now</button></p>
        </div>
      </form>
    </div>
  );
};

export default Login;
