import React, { useState, useEffect } from 'react';
import '../styles/Prescription.css';

const Prescription = ({ patientId, patientName }) => {
  const [prescription, setPrescription] = useState({
    date: new Date().toISOString().split('T')[0],
    diagnosis: '',
    medications: [
      {
        name: '',
        dosage: '',
        frequency: '',
        duration: '',
        instructions: ''
      }
    ],
    notes: '',
    followUpDate: ''
  });

  const [doctorInfo, setDoctorInfo] = useState({
    name: '',
    rank: '',
    serviceNumber: '',
    specialization: ''
  });

  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previousPrescriptions, setPreviousPrescriptions] = useState([]);
  const [showPreviousPrescriptions, setShowPreviousPrescriptions] = useState(false);
  const [errors, setErrors] = useState({});

  // Check if the current user is authorized to prescribe medications
  useEffect(() => {
    const userRole = localStorage.getItem('userRole');
    const userName = localStorage.getItem('userName') || '';
    const userRank = localStorage.getItem('userRank') || '';
    const userServiceNumber = localStorage.getItem('userServiceNumber') || '';
    
    // Only doctors and medical officers can prescribe
    const authorized = userRole === 'doctor' || userRole === 'medical_officer' || userRole === 'officer';
    setIsAuthorized(authorized);
    
    if (authorized) {
      setDoctorInfo({
        name: userName,
        rank: userRank,
        serviceNumber: userServiceNumber,
        specialization: 'Medical Officer' // This would come from the user profile in a real app
      });
    }

    // Load previous prescriptions from localStorage (in a real app, this would come from a database)
    const storedPrescriptions = JSON.parse(localStorage.getItem(`prescriptions_${patientId}`) || '[]');
    setPreviousPrescriptions(storedPrescriptions);
  }, [patientId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setPrescription(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleMedicationChange = (index, field, value) => {
    const updatedMedications = [...prescription.medications];
    updatedMedications[index] = { ...updatedMedications[index], [field]: value };
    setPrescription(prev => ({ ...prev, medications: updatedMedications }));
    
    // Clear medication errors
    if (errors[`medication_${index}_${field}`]) {
      setErrors(prev => ({ ...prev, [`medication_${index}_${field}`]: null }));
    }
  };

  const addMedication = () => {
    setPrescription(prev => ({
      ...prev,
      medications: [
        ...prev.medications,
        {
          name: '',
          dosage: '',
          frequency: '',
          duration: '',
          instructions: ''
        }
      ]
    }));
  };

  const removeMedication = (index) => {
    if (prescription.medications.length > 1) {
      const updatedMedications = [...prescription.medications];
      updatedMedications.splice(index, 1);
      setPrescription(prev => ({ ...prev, medications: updatedMedications }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!prescription.diagnosis.trim()) {
      newErrors.diagnosis = 'Diagnosis is required';
    }
    
    prescription.medications.forEach((med, index) => {
      if (!med.name.trim()) {
        newErrors[`medication_${index}_name`] = 'Medication name is required';
      }
      if (!med.dosage.trim()) {
        newErrors[`medication_${index}_dosage`] = 'Dosage is required';
      }
      if (!med.frequency.trim()) {
        newErrors[`medication_${index}_frequency`] = 'Frequency is required';
      }
      if (!med.duration.trim()) {
        newErrors[`medication_${index}_duration`] = 'Duration is required';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!isAuthorized) {
      alert('You are not authorized to prescribe medications.');
      return;
    }
    
    if (validateForm()) {
      setIsSubmitting(true);
      
      // Create a new prescription record with doctor information
      const newPrescription = {
        ...prescription,
        prescribedBy: {
          name: doctorInfo.name,
          rank: doctorInfo.rank,
          serviceNumber: doctorInfo.serviceNumber,
          specialization: doctorInfo.specialization
        },
        patientName: patientName,
        patientId: patientId,
        prescribedAt: new Date().toISOString()
      };
      
      // In a real app, you would send this to your backend
      // For demo purposes, we'll store it in localStorage
      const updatedPrescriptions = [newPrescription, ...previousPrescriptions];
      localStorage.setItem(`prescriptions_${patientId}`, JSON.stringify(updatedPrescriptions));
      
      // Update the state
      setPreviousPrescriptions(updatedPrescriptions);
      
      // Reset form
      setPrescription({
        date: new Date().toISOString().split('T')[0],
        diagnosis: '',
        medications: [
          {
            name: '',
            dosage: '',
            frequency: '',
            duration: '',
            instructions: ''
          }
        ],
        notes: '',
        followUpDate: ''
      });
      
      setIsSubmitting(false);
      alert('Prescription created successfully!');
    }
  };

  const togglePreviousPrescriptions = () => {
    setShowPreviousPrescriptions(!showPreviousPrescriptions);
  };

  if (!isAuthorized) {
    return (
      <div className="prescription-container unauthorized">
        <h2>Prescription Management</h2>
        <div className="unauthorized-message">
          <p>You are not authorized to prescribe medications.</p>
          <p>Only medical officers and doctors can access this feature.</p>
        </div>
        
        {previousPrescriptions.length > 0 && (
          <div className="previous-prescriptions-section">
            <h3>Your Prescriptions</h3>
            <div className="prescriptions-list">
              {previousPrescriptions.map((prescription, index) => (
                <div key={index} className="prescription-card">
                  <div className="prescription-header">
                    <div className="prescription-date">
                      <strong>Date:</strong> {prescription.date}
                    </div>
                    <div className="prescription-doctor">
                      <strong>Prescribed by:</strong> {prescription.prescribedBy?.rank} {prescription.prescribedBy?.name}
                    </div>
                  </div>
                  <div className="prescription-body">
                    <div className="prescription-diagnosis">
                      <strong>Diagnosis:</strong> {prescription.diagnosis}
                    </div>
                    <div className="prescription-medications">
                      <strong>Medications:</strong>
                      <ul>
                        {prescription.medications.map((med, medIndex) => (
                          <li key={medIndex}>
                            <strong>{med.name}</strong> - {med.dosage}, {med.frequency}, for {med.duration}
                            {med.instructions && <div className="med-instructions">{med.instructions}</div>}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {prescription.notes && (
                      <div className="prescription-notes">
                        <strong>Notes:</strong> {prescription.notes}
                      </div>
                    )}
                    {prescription.followUpDate && (
                      <div className="prescription-followup">
                        <strong>Follow-up:</strong> {prescription.followUpDate}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="prescription-container">
      <h2>Create Prescription</h2>
      <p className="doctor-info">
        Doctor: {doctorInfo.rank} {doctorInfo.name} ({doctorInfo.specialization})
      </p>
      <p className="patient-info">
        Patient: {patientName} (ID: {patientId})
      </p>
      
      <form onSubmit={handleSubmit} className="prescription-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="date">Date</label>
            <input
              type="date"
              id="date"
              name="date"
              value={prescription.date}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="followUpDate">Follow-up Date (if needed)</label>
            <input
              type="date"
              id="followUpDate"
              name="followUpDate"
              value={prescription.followUpDate}
              onChange={handleChange}
              min={prescription.date}
            />
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="diagnosis">Diagnosis</label>
          <input
            type="text"
            id="diagnosis"
            name="diagnosis"
            value={prescription.diagnosis}
            onChange={handleChange}
            placeholder="Patient's diagnosis"
            required
          />
          {errors.diagnosis && <div className="error-message">{errors.diagnosis}</div>}
        </div>
        
        <div className="medications-section">
          <h3>Medications</h3>
          
          {prescription.medications.map((medication, index) => (
            <div key={index} className="medication-card">
              <div className="medication-header">
                <h4>Medication {index + 1}</h4>
                {prescription.medications.length > 1 && (
                  <button
                    type="button"
                    className="remove-medication-btn"
                    onClick={() => removeMedication(index)}
                  >
                    ×
                  </button>
                )}
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor={`medication-name-${index}`}>Medication Name</label>
                  <input
                    type="text"
                    id={`medication-name-${index}`}
                    value={medication.name}
                    onChange={(e) => handleMedicationChange(index, 'name', e.target.value)}
                    placeholder="e.g., Paracetamol"
                    required
                  />
                  {errors[`medication_${index}_name`] && (
                    <div className="error-message">{errors[`medication_${index}_name`]}</div>
                  )}
                </div>
                
                <div className="form-group">
                  <label htmlFor={`medication-dosage-${index}`}>Dosage</label>
                  <input
                    type="text"
                    id={`medication-dosage-${index}`}
                    value={medication.dosage}
                    onChange={(e) => handleMedicationChange(index, 'dosage', e.target.value)}
                    placeholder="e.g., 500mg"
                    required
                  />
                  {errors[`medication_${index}_dosage`] && (
                    <div className="error-message">{errors[`medication_${index}_dosage`]}</div>
                  )}
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor={`medication-frequency-${index}`}>Frequency</label>
                  <input
                    type="text"
                    id={`medication-frequency-${index}`}
                    value={medication.frequency}
                    onChange={(e) => handleMedicationChange(index, 'frequency', e.target.value)}
                    placeholder="e.g., 3 times daily"
                    required
                  />
                  {errors[`medication_${index}_frequency`] && (
                    <div className="error-message">{errors[`medication_${index}_frequency`]}</div>
                  )}
                </div>
                
                <div className="form-group">
                  <label htmlFor={`medication-duration-${index}`}>Duration</label>
                  <input
                    type="text"
                    id={`medication-duration-${index}`}
                    value={medication.duration}
                    onChange={(e) => handleMedicationChange(index, 'duration', e.target.value)}
                    placeholder="e.g., 5 days"
                    required
                  />
                  {errors[`medication_${index}_duration`] && (
                    <div className="error-message">{errors[`medication_${index}_duration`]}</div>
                  )}
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor={`medication-instructions-${index}`}>Special Instructions</label>
                <textarea
                  id={`medication-instructions-${index}`}
                  value={medication.instructions}
                  onChange={(e) => handleMedicationChange(index, 'instructions', e.target.value)}
                  placeholder="e.g., Take after meals"
                  rows="2"
                ></textarea>
              </div>
            </div>
          ))}
          
          <button
            type="button"
            className="add-medication-btn"
            onClick={addMedication}
          >
            + Add Another Medication
          </button>
        </div>
        
        <div className="form-group">
          <label htmlFor="notes">Additional Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={prescription.notes}
            onChange={handleChange}
            rows="3"
            placeholder="Any additional instructions or notes"
          ></textarea>
        </div>
        
        <div className="form-actions">
          <button type="submit" className="submit-button" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Create Prescription'}
          </button>
        </div>
      </form>
      
      {previousPrescriptions.length > 0 && (
        <div className="previous-prescriptions-section">
          <button 
            className="toggle-prescriptions-button"
            onClick={togglePreviousPrescriptions}
          >
            {showPreviousPrescriptions ? 'Hide Previous Prescriptions' : 'Show Previous Prescriptions'}
          </button>
          
          {showPreviousPrescriptions && (
            <div className="previous-prescriptions">
              <h3>Previous Prescriptions</h3>
              <div className="prescriptions-list">
                {previousPrescriptions.map((prescription, index) => (
                  <div key={index} className="prescription-card">
                    <div className="prescription-header">
                      <div className="prescription-date">
                        <strong>Date:</strong> {prescription.date}
                      </div>
                      <div className="prescription-doctor">
                        <strong>Prescribed by:</strong> {prescription.prescribedBy?.rank} {prescription.prescribedBy?.name}
                      </div>
                    </div>
                    <div className="prescription-body">
                      <div className="prescription-diagnosis">
                        <strong>Diagnosis:</strong> {prescription.diagnosis}
                      </div>
                      <div className="prescription-medications">
                        <strong>Medications:</strong>
                        <ul>
                          {prescription.medications.map((med, medIndex) => (
                            <li key={medIndex}>
                              <strong>{med.name}</strong> - {med.dosage}, {med.frequency}, for {med.duration}
                              {med.instructions && <div className="med-instructions">{med.instructions}</div>}
                            </li>
                          ))}
                        </ul>
                      </div>
                      {prescription.notes && (
                        <div className="prescription-notes">
                          <strong>Notes:</strong> {prescription.notes}
                        </div>
                      )}
                      {prescription.followUpDate && (
                        <div className="prescription-followup">
                          <strong>Follow-up:</strong> {prescription.followUpDate}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Prescription;
