.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
}

.login-form {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-form h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #003366;
  text-align: center;
}

.error {
  color: #e74c3c;
  margin-bottom: 1rem;
  text-align: center;
}

.login-form input {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.login-form button[type="submit"] {
  width: 100%;
  padding: 0.75rem;
  background-color: #003366;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-form button[type="submit"]:hover {
  background-color: #002244;
}

.login-form button[type="submit"]:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.login-form p {
  text-align: center;
  margin-top: 1rem;
}

.login-form p button {
  background: none;
  border: none;
  color: #003366;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
}

.login-form p button:hover {
  text-decoration: underline;
}
