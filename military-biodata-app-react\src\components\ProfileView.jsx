import React, { useState, useEffect } from 'react';
import '../styles/ProfileView.css';

const ProfileView = ({ user }) => {
  const [personalInfo, setPersonalInfo] = useState({
    name: 'Loading...',
    rank: 'Loading...',
    serviceNumber: 'Loading...',
    branch: 'Loading...',
    division: 'Loading...',
    dateOfBirth: 'Loading...',
    age: 'Loading...',
    sex: 'Loading...',
    maritalStatus: 'Loading...',
    stateOfOrigin: 'Loading...',
    lga: 'Loading...',
    bloodType: 'Loading...',
    genotype: 'Loading...',
    height: 'Loading...',
    weight: 'Loading...',
    nin: 'Loading...',
    phoneNumber: 'Loading...',
    residentialAddress: 'Loading...',
    nextOfKin: 'Loading...',
    nextOfKinRelationship: 'Loading...',
    nextOfKinContact: 'Loading...'
  });

  const [spouseInfo, setSpouseInfo] = useState({
    name: '',
    title: '',
    occupation: '',
    phoneNumber: '',
    address: '',
    branch: '',
    rank: '',
    serviceNumber: ''
  });

  const [childrenInfo, setChildrenInfo] = useState([]);
  const [activeTab, setActiveTab] = useState('personal');
  const [isEditing, setIsEditing] = useState(false);
  const [editableInfo, setEditableInfo] = useState({});

  // Load user data
  useEffect(() => {
    if (user) {
      // In a real app, this would be an API call
      // For demo purposes, we'll use some sample data
      
      // Check if we have stored data for this user
      const storedPersonalInfo = localStorage.getItem(`personalInfo_${user.id}`);
      const storedSpouseInfo = localStorage.getItem(`spouseInfo_${user.id}`);
      const storedChildrenInfo = localStorage.getItem(`childrenInfo_${user.id}`);
      
      if (storedPersonalInfo) {
        setPersonalInfo(JSON.parse(storedPersonalInfo));
      } else {
        // Use default data based on user type
        if (user.role === 'officer') {
          setPersonalInfo({
            name: user.name,
            rank: user.rank,
            serviceNumber: user.serviceNumber,
            branch: user.branch,
            division: user.division || 'Logistics Command',
            dateOfBirth: '15/05/1985',
            age: '38',
            sex: 'Male',
            maritalStatus: 'Married',
            stateOfOrigin: 'Lagos',
            lga: 'Ikeja',
            bloodType: 'O+',
            genotype: 'AA',
            height: '182 cm',
            weight: '78 kg',
            nin: '12345678901',
            phoneNumber: '08012345678',
            residentialAddress: '123 Military Quarters, Ikeja Cantonment, Lagos',
            nextOfKin: 'Jane Doe',
            nextOfKinRelationship: 'Wife',
            nextOfKinContact: '08087654321'
          });
        } else {
          setPersonalInfo({
            name: user.name,
            rank: user.rank,
            serviceNumber: user.serviceNumber,
            branch: user.branch,
            division: user.division || 'Logistics Command',
            dateOfBirth: '10/08/1990',
            age: '33',
            sex: 'Male',
            maritalStatus: 'Married',
            stateOfOrigin: 'Rivers',
            lga: 'Port Harcourt',
            bloodType: 'B+',
            genotype: 'AA',
            height: '175 cm',
            weight: '80 kg',
            nin: '98765432101',
            phoneNumber: '08098765432',
            residentialAddress: '456 Naval Base Quarters, Apapa, Lagos',
            nextOfKin: 'Mary Smith',
            nextOfKinRelationship: 'Wife',
            nextOfKinContact: '08076543210'
          });
        }
      }
      
      if (storedSpouseInfo) {
        setSpouseInfo(JSON.parse(storedSpouseInfo));
      } else {
        // Default spouse info
        setSpouseInfo({
          name: 'Jane Doe',
          title: 'Mrs',
          occupation: 'Teacher',
          phoneNumber: '08087654321',
          address: '123 Military Quarters, Ikeja Cantonment, Lagos',
          branch: '',
          rank: '',
          serviceNumber: ''
        });
      }
      
      if (storedChildrenInfo) {
        setChildrenInfo(JSON.parse(storedChildrenInfo));
      } else {
        // Default children info
        setChildrenInfo([
          {
            id: 1,
            name: 'John Doe Jr.',
            dateOfBirth: '12/06/2010',
            age: '13',
            gender: 'Male',
            school: 'Nigerian Navy Secondary School'
          },
          {
            id: 2,
            name: 'Jane Doe',
            dateOfBirth: '25/09/2015',
            age: '8',
            gender: 'Female',
            school: 'Nigerian Navy Primary School'
          }
        ]);
      }
    }
  }, [user]);

  const handleEditToggle = () => {
    if (isEditing) {
      // Save changes
      switch (activeTab) {
        case 'personal':
          localStorage.setItem(`personalInfo_${user.id}`, JSON.stringify(editableInfo));
          setPersonalInfo(editableInfo);
          break;
        case 'spouse':
          localStorage.setItem(`spouseInfo_${user.id}`, JSON.stringify(editableInfo));
          setSpouseInfo(editableInfo);
          break;
        case 'children':
          localStorage.setItem(`childrenInfo_${user.id}`, JSON.stringify(editableInfo));
          setChildrenInfo(editableInfo);
          break;
        default:
          break;
      }
    } else {
      // Start editing
      switch (activeTab) {
        case 'personal':
          setEditableInfo(personalInfo);
          break;
        case 'spouse':
          setEditableInfo(spouseInfo);
          break;
        case 'children':
          setEditableInfo(childrenInfo);
          break;
        default:
          break;
      }
    }
    
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditableInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleChildInputChange = (id, field, value) => {
    setEditableInfo(prev => 
      prev.map(child => 
        child.id === id ? { ...child, [field]: value } : child
      )
    );
  };

  const addChild = () => {
    const newChild = {
      id: Date.now(),
      name: '',
      dateOfBirth: '',
      age: '',
      gender: '',
      school: ''
    };
    
    setEditableInfo([...editableInfo, newChild]);
  };

  const removeChild = (id) => {
    setEditableInfo(prev => prev.filter(child => child.id !== id));
  };

  const renderPersonalInfoTab = () => {
    const data = isEditing ? editableInfo : personalInfo;
    
    return (
      <div className="profile-tab-content">
        <div className="profile-section">
          <h3>Basic Information</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">Name:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="name" 
                  value={data.name} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.name}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Rank:</span>
              <span className="info-value">{data.rank}</span>
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Service Number:</span>
              <span className="info-value">{data.serviceNumber}</span>
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Branch:</span>
              <span className="info-value">{data.branch}</span>
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Division:</span>
              <span className="info-value">{data.division}</span>
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Date of Birth:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="dateOfBirth" 
                  value={data.dateOfBirth} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.dateOfBirth}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Age:</span>
              <span className="info-value">{data.age}</span>
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Sex:</span>
              {isEditing ? (
                <select 
                  name="sex" 
                  value={data.sex} 
                  onChange={handleInputChange} 
                  className="edit-input"
                >
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                </select>
              ) : (
                <span className="info-value">{data.sex}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Marital Status:</span>
              {isEditing ? (
                <select 
                  name="maritalStatus" 
                  value={data.maritalStatus} 
                  onChange={handleInputChange} 
                  className="edit-input"
                >
                  <option value="Single">Single</option>
                  <option value="Married">Married</option>
                  <option value="Divorced">Divorced</option>
                  <option value="Widowed">Widowed</option>
                </select>
              ) : (
                <span className="info-value">{data.maritalStatus}</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="profile-section">
          <h3>Medical Information</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">Blood Type:</span>
              {isEditing ? (
                <select 
                  name="bloodType" 
                  value={data.bloodType} 
                  onChange={handleInputChange} 
                  className="edit-input"
                >
                  <option value="A+">A+</option>
                  <option value="A-">A-</option>
                  <option value="B+">B+</option>
                  <option value="B-">B-</option>
                  <option value="AB+">AB+</option>
                  <option value="AB-">AB-</option>
                  <option value="O+">O+</option>
                  <option value="O-">O-</option>
                </select>
              ) : (
                <span className="info-value">{data.bloodType}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Genotype:</span>
              {isEditing ? (
                <select 
                  name="genotype" 
                  value={data.genotype} 
                  onChange={handleInputChange} 
                  className="edit-input"
                >
                  <option value="AA">AA</option>
                  <option value="AS">AS</option>
                  <option value="AC">AC</option>
                  <option value="SS">SS</option>
                  <option value="SC">SC</option>
                </select>
              ) : (
                <span className="info-value">{data.genotype}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Height:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="height" 
                  value={data.height} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.height}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Weight:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="weight" 
                  value={data.weight} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.weight}</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="profile-section">
          <h3>Contact Information</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">NIN:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="nin" 
                  value={data.nin} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.nin}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Phone Number:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="phoneNumber" 
                  value={data.phoneNumber} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.phoneNumber}</span>
              )}
            </div>
            
            <div className="profile-info-item full-width">
              <span className="info-label">Residential Address:</span>
              {isEditing ? (
                <textarea 
                  name="residentialAddress" 
                  value={data.residentialAddress} 
                  onChange={handleInputChange} 
                  className="edit-input textarea"
                  rows="2"
                />
              ) : (
                <span className="info-value">{data.residentialAddress}</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="profile-section">
          <h3>Next of Kin</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">Name:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="nextOfKin" 
                  value={data.nextOfKin} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.nextOfKin}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Relationship:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="nextOfKinRelationship" 
                  value={data.nextOfKinRelationship} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.nextOfKinRelationship}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Contact:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="nextOfKinContact" 
                  value={data.nextOfKinContact} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.nextOfKinContact}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderSpouseInfoTab = () => {
    const data = isEditing ? editableInfo : spouseInfo;
    
    return (
      <div className="profile-tab-content">
        <div className="profile-section">
          <h3>Spouse Information</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">Name:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="name" 
                  value={data.name} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.name}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Title:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="title" 
                  value={data.title} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.title}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Occupation:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="occupation" 
                  value={data.occupation} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.occupation}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Phone Number:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="phoneNumber" 
                  value={data.phoneNumber} 
                  onChange={handleInputChange} 
                  className="edit-input"
                />
              ) : (
                <span className="info-value">{data.phoneNumber}</span>
              )}
            </div>
            
            <div className="profile-info-item full-width">
              <span className="info-label">Address:</span>
              {isEditing ? (
                <textarea 
                  name="address" 
                  value={data.address} 
                  onChange={handleInputChange} 
                  className="edit-input textarea"
                  rows="2"
                />
              ) : (
                <span className="info-value">{data.address}</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="profile-section">
          <h3>Military Information (If Applicable)</h3>
          <div className="profile-info-grid">
            <div className="profile-info-item">
              <span className="info-label">Branch:</span>
              {isEditing ? (
                <select 
                  name="branch" 
                  value={data.branch} 
                  onChange={handleInputChange} 
                  className="edit-input"
                >
                  <option value="">Not Applicable</option>
                  <option value="Navy">Navy</option>
                  <option value="Army">Army</option>
                  <option value="AirForce">Air Force</option>
                </select>
              ) : (
                <span className="info-value">{data.branch || 'Not Applicable'}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Rank:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="rank" 
                  value={data.rank} 
                  onChange={handleInputChange} 
                  className="edit-input"
                  placeholder="If applicable"
                />
              ) : (
                <span className="info-value">{data.rank || 'Not Applicable'}</span>
              )}
            </div>
            
            <div className="profile-info-item">
              <span className="info-label">Service Number:</span>
              {isEditing ? (
                <input 
                  type="text" 
                  name="serviceNumber" 
                  value={data.serviceNumber} 
                  onChange={handleInputChange} 
                  className="edit-input"
                  placeholder="If applicable"
                />
              ) : (
                <span className="info-value">{data.serviceNumber || 'Not Applicable'}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderChildrenInfoTab = () => {
    const data = isEditing ? editableInfo : childrenInfo;
    
    return (
      <div className="profile-tab-content">
        <div className="profile-section">
          <h3>Children Information</h3>
          
          {data.length === 0 ? (
            <p className="no-data-message">No children information available.</p>
          ) : (
            data.map((child, index) => (
              <div key={child.id} className="child-card">
                <div className="child-card-header">
                  <h4>Child {index + 1}</h4>
                  {isEditing && (
                    <button 
                      type="button" 
                      className="remove-child-btn"
                      onClick={() => removeChild(child.id)}
                    >
                      Remove
                    </button>
                  )}
                </div>
                
                <div className="profile-info-grid">
                  <div className="profile-info-item">
                    <span className="info-label">Name:</span>
                    {isEditing ? (
                      <input 
                        type="text" 
                        value={child.name} 
                        onChange={(e) => handleChildInputChange(child.id, 'name', e.target.value)} 
                        className="edit-input"
                      />
                    ) : (
                      <span className="info-value">{child.name}</span>
                    )}
                  </div>
                  
                  <div className="profile-info-item">
                    <span className="info-label">Date of Birth:</span>
                    {isEditing ? (
                      <input 
                        type="text" 
                        value={child.dateOfBirth} 
                        onChange={(e) => handleChildInputChange(child.id, 'dateOfBirth', e.target.value)} 
                        className="edit-input"
                      />
                    ) : (
                      <span className="info-value">{child.dateOfBirth}</span>
                    )}
                  </div>
                  
                  <div className="profile-info-item">
                    <span className="info-label">Age:</span>
                    {isEditing ? (
                      <input 
                        type="text" 
                        value={child.age} 
                        onChange={(e) => handleChildInputChange(child.id, 'age', e.target.value)} 
                        className="edit-input"
                      />
                    ) : (
                      <span className="info-value">{child.age}</span>
                    )}
                  </div>
                  
                  <div className="profile-info-item">
                    <span className="info-label">Gender:</span>
                    {isEditing ? (
                      <select 
                        value={child.gender} 
                        onChange={(e) => handleChildInputChange(child.id, 'gender', e.target.value)} 
                        className="edit-input"
                      >
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    ) : (
                      <span className="info-value">{child.gender}</span>
                    )}
                  </div>
                  
                  <div className="profile-info-item">
                    <span className="info-label">School:</span>
                    {isEditing ? (
                      <input 
                        type="text" 
                        value={child.school} 
                        onChange={(e) => handleChildInputChange(child.id, 'school', e.target.value)} 
                        className="edit-input"
                      />
                    ) : (
                      <span className="info-value">{child.school}</span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
          
          {isEditing && (
            <button 
              type="button" 
              className="add-child-btn"
              onClick={addChild}
            >
              + Add Child
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="profile-view-container">
      <div className="profile-header">
        <div className="profile-avatar-section">
          <img src={user?.avatar || '/images/logos/NN.png'} alt={user?.name} className="profile-avatar" />
          <h2>{user?.name}</h2>
          <p>{user?.rank}</p>
          <p>{user?.serviceNumber}</p>
        </div>
        
        <div className="profile-actions">
          <button 
            className={`edit-button ${isEditing ? 'save-mode' : ''}`}
            onClick={handleEditToggle}
          >
            {isEditing ? 'Save Changes' : 'Edit Profile'}
          </button>
        </div>
      </div>
      
      <div className="profile-tabs">
        <button 
          className={`tab-button ${activeTab === 'personal' ? 'active' : ''}`}
          onClick={() => {
            if (isEditing) {
              if (window.confirm('You have unsaved changes. Discard changes?')) {
                setIsEditing(false);
                setActiveTab('personal');
              }
            } else {
              setActiveTab('personal');
            }
          }}
        >
          Personal Information
        </button>
        
        <button 
          className={`tab-button ${activeTab === 'spouse' ? 'active' : ''}`}
          onClick={() => {
            if (isEditing) {
              if (window.confirm('You have unsaved changes. Discard changes?')) {
                setIsEditing(false);
                setActiveTab('spouse');
              }
            } else {
              setActiveTab('spouse');
            }
          }}
        >
          Spouse Information
        </button>
        
        <button 
          className={`tab-button ${activeTab === 'children' ? 'active' : ''}`}
          onClick={() => {
            if (isEditing) {
              if (window.confirm('You have unsaved changes. Discard changes?')) {
                setIsEditing(false);
                setActiveTab('children');
              }
            } else {
              setActiveTab('children');
            }
          }}
        >
          Children Information
        </button>
      </div>
      
      <div className="profile-content">
        {activeTab === 'personal' && renderPersonalInfoTab()}
        {activeTab === 'spouse' && renderSpouseInfoTab()}
        {activeTab === 'children' && renderChildrenInfoTab()}
      </div>
    </div>
  );
};

export default ProfileView;
