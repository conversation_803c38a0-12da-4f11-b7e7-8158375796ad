.med-blog {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.blog-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.blog-header h1 {
  color: #0a3d62;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blog-header h1 svg {
  margin-right: 0.5rem;
  color: #e74c3c;
}

.blog-header p {
  color: #555;
  font-size: 1.2rem;
}

.featured-article {
  display: flex;
  margin-bottom: 3rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.article-image {
  flex: 1;
  min-height: 300px;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-content {
  flex: 1;
  padding: 2rem;
}

.article-tag {
  background-color: #e74c3c;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 1rem;
}

.article-content h2 {
  color: #0a3d62;
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.8rem;
}

.article-meta {
  color: #777;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.article-excerpt {
  color: #444;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.read-more {
  background-color: #0a3d62;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.read-more:hover {
  background-color: #0c2461;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.blog-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-icon {
  font-size: 2rem;
  color: #0a3d62;
  margin-bottom: 1rem;
}

.blog-card h3 {
  color: #0a3d62;
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.blog-card p {
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.read-time {
  color: #777;
  font-size: 0.8rem;
  display: block;
}

.contribute-banner {
  background: linear-gradient(135deg, #0a3d62, #3c6382);
  color: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 3rem;
}

.contribute-banner h3 {
  margin-top: 0;
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.contribute-banner p {
  margin-bottom: 1.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contribute-banner button {
  background-color: white;
  color: #0a3d62;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contribute-banner button:hover {
  background-color: #f1f1f1;
}

.comments-section {
  background-color: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.comments-section h3 {
  display: flex;
  align-items: center;
  color: #0a3d62;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.comments-section h3 svg {
  margin-right: 0.5rem;
}

.comment-form {
  margin-bottom: 2rem;
}

.comment-form textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 100px;
  margin-bottom: 1rem;
  font-family: inherit;
}

.comment-form button {
  background-color: #0a3d62;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.comment {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 1rem;
}

.comment-author {
  font-weight: bold;
  color: #0a3d62;
  display: block;
}

.comment-date {
  font-size: 0.8rem;
  color: #777;
}

.comment-text {
  color: #444;
  line-height: 1.5;
  margin: 0;
}

@media (max-width: 768px) {
  .featured-article {
    flex-direction: column;
  }
  
  .article-image {
    min-height: 200px;
  }
  
  .blog-grid {
    grid-template-columns: 1fr;
  }
}

