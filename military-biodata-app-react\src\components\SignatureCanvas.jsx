import React, { useRef, useState, useEffect } from 'react';
import './SignatureCanvas.css';

const SignatureCanvas = ({ onSave, initialSignature = null }) => {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [ctx, setCtx] = useState(null);
  const [signatureData, setSignatureData] = useState(initialSignature);
  const [isEmpty, setIsEmpty] = useState(!initialSignature);

  // Initialize canvas context
  useEffect(() => {
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    // Set canvas properties
    context.lineWidth = 2;
    context.lineCap = 'round';
    context.strokeStyle = '#000000';
    
    setCtx(context);
    
    // Load initial signature if provided
    if (initialSignature) {
      const img = new Image();
      img.onload = () => {
        context.drawImage(img, 0, 0);
      };
      img.src = initialSignature;
    }
    
    // Clear canvas on resize
    const handleResize = () => {
      const savedData = canvas.toDataURL();
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
      
      // Restore canvas properties after resize
      context.lineWidth = 2;
      context.lineCap = 'round';
      context.strokeStyle = '#000000';
      
      // Restore the signature
      if (savedData && savedData !== 'data:,') {
        const img = new Image();
        img.onload = () => {
          context.drawImage(img, 0, 0);
        };
        img.src = savedData;
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [initialSignature]);

  // Start drawing
  const startDrawing = (e) => {
    const { offsetX, offsetY } = getCoordinates(e);
    ctx.beginPath();
    ctx.moveTo(offsetX, offsetY);
    setIsDrawing(true);
    setIsEmpty(false);
  };

  // Draw
  const draw = (e) => {
    if (!isDrawing) return;
    
    const { offsetX, offsetY } = getCoordinates(e);
    ctx.lineTo(offsetX, offsetY);
    ctx.stroke();
  };

  // Stop drawing
  const stopDrawing = () => {
    if (isDrawing) {
      ctx.closePath();
      setIsDrawing(false);
      
      // Save signature data
      const data = canvasRef.current.toDataURL('image/png');
      setSignatureData(data);
      if (onSave) {
        onSave(data);
      }
    }
  };

  // Get coordinates for both mouse and touch events
  const getCoordinates = (e) => {
    let offsetX, offsetY;
    const canvas = canvasRef.current;
    
    if (e.type.includes('touch')) {
      const rect = canvas.getBoundingClientRect();
      const touch = e.touches[0] || e.changedTouches[0];
      offsetX = touch.clientX - rect.left;
      offsetY = touch.clientY - rect.top;
    } else {
      offsetX = e.nativeEvent.offsetX;
      offsetY = e.nativeEvent.offsetY;
    }
    
    return { offsetX, offsetY };
  };

  // Clear the canvas
  const clearCanvas = () => {
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    setSignatureData(null);
    setIsEmpty(true);
    if (onSave) {
      onSave(null);
    }
  };

  return (
    <div className="signature-container">
      <div className="signature-header">
        <h3>Draw Your Signature</h3>
        <button type="button" className="clear-button" onClick={clearCanvas}>
          Clear
        </button>
      </div>
      <div className="canvas-container">
        <canvas
          ref={canvasRef}
          className="signature-canvas"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
        />
      </div>
      {isEmpty && (
        <p className="signature-hint">Please sign above</p>
      )}
    </div>
  );
};

export default SignatureCanvas;
