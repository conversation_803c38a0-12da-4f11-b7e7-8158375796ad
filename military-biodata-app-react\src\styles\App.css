.app {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-header {
  background-color: #003366;
  color: white;
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.main-nav {
  display: flex;
  gap: 1rem;
}

.nav-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.nav-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-button.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-profile-dropdown {
  position: relative;
}

.user-profile-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-profile-header:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.user-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.user-name-header {
  font-size: 0.9rem;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.7rem;
  margin-left: 0.25rem;
  opacity: 0.7;
}

.profile-dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 250px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 1000;
  display: none;
}

.user-profile-dropdown:hover .profile-dropdown-content {
  display: block;
}

.dropdown-user-info {
  padding: 1rem;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #003366;
}

.dropdown-user-details h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: #003366;
}

.dropdown-user-details p {
  margin: 0;
  font-size: 0.8rem;
  color: #555;
}

.dropdown-links {
  display: flex;
  flex-direction: column;
}

.dropdown-link {
  padding: 0.75rem 1rem;
  text-align: left;
  background: none;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.dropdown-link:hover {
  background-color: #f5f5f5;
}

.dropdown-link:last-child {
  border-bottom: none;
}

.dropdown-icon {
  font-size: 1.1rem;
}

.app-content {
  flex: 1;
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.logout-button {
  background-color: rgba(255, 59, 48, 0.8);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgb(255, 59, 48);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .main-nav {
    width: 100%;
    justify-content: center;
  }
}
/* Demo styles */
.demo-header {
  background-color: #003366;
  color: white;
  padding: 1rem 2rem;
  text-align: center;
}

.demo-header h1 {
  margin: 0 0 1rem 0;
}

.mode-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.mode-selector button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mode-selector button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.mode-selector button.active {
  background-color: rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.thumbnail-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.thumbnail-container h2 {
  margin-top: 0;
  margin-bottom: 2rem;
  color: #333;
}
