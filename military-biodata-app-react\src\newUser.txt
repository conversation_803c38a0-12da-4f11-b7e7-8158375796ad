﻿          const newUser = {
            id: personnelId,
            email: formData.email,
            name: `${formData.title} ${formData.firstName} ${formData.surname}`,
            rank: formData.rank,
            serviceNumber: formData.serviceNumber,
            service: formData.service,
            command: "Logistics Command",
            role: formData.personnelCategory.includes("Commissioned Officer") ? "officer" : "enlisted",
            avatar: "/images/logos/NN.png"
          };

          // Store personal information
          const personalInfo = {
            name: `${formData.title} ${formData.firstName} ${formData.surname}`,
            rank: formData.rank,
            serviceNumber: formData.serviceNumber,
            service: formData.service,
            personnelCategory: formData.personnelCategory,
            isCommissionedOfficer: formData.personnelCategory.includes("Commissioned Officer"),
            command: "Logistics Command",
            dateOfBirth: formData.dateOfBirth,
            age: calculateAge(formData.dateOfBirth),
            sex: formData.sex,
            maritalStatus: formData.maritalStatus,
            stateOfOrigin: formData.stateOfOrigin,
            lga: formData.lga,
            bloodType: formData.bloodGroup,
            genotype: formData.genotype,
            height: formData.height,
            weight: formData.weight,
            nin: formData.nin,
            phoneNumber: formData.phoneNumber,
            residentialAddress: formData.residentialAddress,
            nextOfKin: formData.nextOfKin,
            nextOfKinRelationship: formData.nextOfKinRelationship,
            nextOfKinContact: formData.nextOfKinContact,
            medicalCondition: formData.medicalCondition,
            otherMedicalCondition: formData.otherMedicalCondition
          };
