import React, { useState, useEffect } from 'react';
import '../styles/VitalSigns.css';

const VitalSigns = ({ patientId, onSave }) => {
  const [vitalSigns, setVitalSigns] = useState({
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().split(' ')[0].substring(0, 5),
    temperature: '',
    bloodPressureSystolic: '',
    bloodPressureDiastolic: '',
    pulseRate: '',
    respiratoryRate: '',
    oxygenSaturation: '',
    weight: '',
    height: '',
    bmi: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [nurseInfo, setNurseInfo] = useState({
    name: '',
    rank: '',
    serviceNumber: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previousReadings, setPreviousReadings] = useState([]);
  const [showPreviousReadings, setShowPreviousReadings] = useState(false);

  // Load nurse info from localStorage
  useEffect(() => {
    const userName = localStorage.getItem('userName') || '';
    const userRank = localStorage.getItem('userRank') || '';
    const userServiceNumber = localStorage.getItem('userServiceNumber') || '';
    
    setNurseInfo({
      name: userName,
      rank: userRank,
      serviceNumber: userServiceNumber
    });

    // Load previous readings from localStorage (in a real app, this would come from a database)
    const storedReadings = JSON.parse(localStorage.getItem(`vitalSigns_${patientId}`) || '[]');
    setPreviousReadings(storedReadings);
  }, [patientId]);

  // Calculate BMI when weight or height changes
  useEffect(() => {
    if (vitalSigns.weight && vitalSigns.height) {
      const weightKg = parseFloat(vitalSigns.weight);
      const heightM = parseFloat(vitalSigns.height) / 100; // convert cm to m
      if (weightKg > 0 && heightM > 0) {
        const bmi = (weightKg / (heightM * heightM)).toFixed(1);
        setVitalSigns(prev => ({ ...prev, bmi }));
      }
    }
  }, [vitalSigns.weight, vitalSigns.height]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setVitalSigns(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!vitalSigns.temperature) newErrors.temperature = 'Temperature is required';
    else if (parseFloat(vitalSigns.temperature) < 35 || parseFloat(vitalSigns.temperature) > 42) 
      newErrors.temperature = 'Temperature should be between 35°C and 42°C';
    
    if (!vitalSigns.bloodPressureSystolic) newErrors.bloodPressureSystolic = 'Systolic BP is required';
    else if (parseFloat(vitalSigns.bloodPressureSystolic) < 70 || parseFloat(vitalSigns.bloodPressureSystolic) > 220) 
      newErrors.bloodPressureSystolic = 'Systolic BP should be between 70 and 220 mmHg';
    
    if (!vitalSigns.bloodPressureDiastolic) newErrors.bloodPressureDiastolic = 'Diastolic BP is required';
    else if (parseFloat(vitalSigns.bloodPressureDiastolic) < 40 || parseFloat(vitalSigns.bloodPressureDiastolic) > 120) 
      newErrors.bloodPressureDiastolic = 'Diastolic BP should be between 40 and 120 mmHg';
    
    if (!vitalSigns.pulseRate) newErrors.pulseRate = 'Pulse rate is required';
    else if (parseFloat(vitalSigns.pulseRate) < 40 || parseFloat(vitalSigns.pulseRate) > 200) 
      newErrors.pulseRate = 'Pulse rate should be between 40 and 200 bpm';
    
    if (!vitalSigns.respiratoryRate) newErrors.respiratoryRate = 'Respiratory rate is required';
    else if (parseFloat(vitalSigns.respiratoryRate) < 8 || parseFloat(vitalSigns.respiratoryRate) > 40) 
      newErrors.respiratoryRate = 'Respiratory rate should be between 8 and 40 breaths/min';
    
    if (!vitalSigns.oxygenSaturation) newErrors.oxygenSaturation = 'Oxygen saturation is required';
    else if (parseFloat(vitalSigns.oxygenSaturation) < 70 || parseFloat(vitalSigns.oxygenSaturation) > 100) 
      newErrors.oxygenSaturation = 'Oxygen saturation should be between 70% and 100%';
    
    if (!vitalSigns.weight) newErrors.weight = 'Weight is required';
    if (!vitalSigns.height) newErrors.height = 'Height is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsSubmitting(true);
      
      // Create a new vital signs record with nurse information
      const newRecord = {
        ...vitalSigns,
        recordedBy: {
          name: nurseInfo.name,
          rank: nurseInfo.rank,
          serviceNumber: nurseInfo.serviceNumber
        },
        recordedAt: new Date().toISOString()
      };
      
      // In a real app, you would send this to your backend
      // For demo purposes, we'll store it in localStorage
      const updatedReadings = [newRecord, ...previousReadings];
      localStorage.setItem(`vitalSigns_${patientId}`, JSON.stringify(updatedReadings));
      
      // Update the state
      setPreviousReadings(updatedReadings);
      
      // Reset form
      setVitalSigns({
        date: new Date().toISOString().split('T')[0],
        time: new Date().toTimeString().split(' ')[0].substring(0, 5),
        temperature: '',
        bloodPressureSystolic: '',
        bloodPressureDiastolic: '',
        pulseRate: '',
        respiratoryRate: '',
        oxygenSaturation: '',
        weight: '',
        height: '',
        bmi: '',
        notes: ''
      });
      
      setIsSubmitting(false);
      
      // Notify parent component
      if (onSave) {
        onSave(newRecord);
      }
      
      alert('Vital signs recorded successfully!');
    }
  };

  const togglePreviousReadings = () => {
    setShowPreviousReadings(!showPreviousReadings);
  };

  return (
    <div className="vital-signs-container">
      <h2>Record Vital Signs</h2>
      <p className="nurse-info">
        Nurse: {nurseInfo.rank} {nurseInfo.name} ({nurseInfo.serviceNumber})
      </p>
      
      <form onSubmit={handleSubmit} className="vital-signs-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="date">Date</label>
            <input
              type="date"
              id="date"
              name="date"
              value={vitalSigns.date}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="time">Time</label>
            <input
              type="time"
              id="time"
              name="time"
              value={vitalSigns.time}
              onChange={handleChange}
              required
            />
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="temperature">Temperature (°C)</label>
            <input
              type="number"
              id="temperature"
              name="temperature"
              value={vitalSigns.temperature}
              onChange={handleChange}
              step="0.1"
              min="35"
              max="42"
              placeholder="36.5"
            />
            {errors.temperature && <div className="error-message">{errors.temperature}</div>}
          </div>
          
          <div className="form-group blood-pressure">
            <label>Blood Pressure (mmHg)</label>
            <div className="bp-inputs">
              <input
                type="number"
                id="bloodPressureSystolic"
                name="bloodPressureSystolic"
                value={vitalSigns.bloodPressureSystolic}
                onChange={handleChange}
                min="70"
                max="220"
                placeholder="120"
              />
              <span>/</span>
              <input
                type="number"
                id="bloodPressureDiastolic"
                name="bloodPressureDiastolic"
                value={vitalSigns.bloodPressureDiastolic}
                onChange={handleChange}
                min="40"
                max="120"
                placeholder="80"
              />
            </div>
            {errors.bloodPressureSystolic && <div className="error-message">{errors.bloodPressureSystolic}</div>}
            {errors.bloodPressureDiastolic && <div className="error-message">{errors.bloodPressureDiastolic}</div>}
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="pulseRate">Pulse Rate (bpm)</label>
            <input
              type="number"
              id="pulseRate"
              name="pulseRate"
              value={vitalSigns.pulseRate}
              onChange={handleChange}
              min="40"
              max="200"
              placeholder="75"
            />
            {errors.pulseRate && <div className="error-message">{errors.pulseRate}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="respiratoryRate">Respiratory Rate (breaths/min)</label>
            <input
              type="number"
              id="respiratoryRate"
              name="respiratoryRate"
              value={vitalSigns.respiratoryRate}
              onChange={handleChange}
              min="8"
              max="40"
              placeholder="16"
            />
            {errors.respiratoryRate && <div className="error-message">{errors.respiratoryRate}</div>}
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="oxygenSaturation">Oxygen Saturation (%)</label>
            <input
              type="number"
              id="oxygenSaturation"
              name="oxygenSaturation"
              value={vitalSigns.oxygenSaturation}
              onChange={handleChange}
              min="70"
              max="100"
              placeholder="98"
            />
            {errors.oxygenSaturation && <div className="error-message">{errors.oxygenSaturation}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="weight">Weight (kg)</label>
            <input
              type="number"
              id="weight"
              name="weight"
              value={vitalSigns.weight}
              onChange={handleChange}
              min="1"
              max="300"
              step="0.1"
              placeholder="70"
            />
            {errors.weight && <div className="error-message">{errors.weight}</div>}
          </div>
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="height">Height (cm)</label>
            <input
              type="number"
              id="height"
              name="height"
              value={vitalSigns.height}
              onChange={handleChange}
              min="50"
              max="250"
              placeholder="170"
            />
            {errors.height && <div className="error-message">{errors.height}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="bmi">BMI</label>
            <input
              type="text"
              id="bmi"
              name="bmi"
              value={vitalSigns.bmi}
              readOnly
              placeholder="Calculated"
            />
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={vitalSigns.notes}
            onChange={handleChange}
            rows="3"
            placeholder="Additional observations or notes"
          ></textarea>
        </div>
        
        <div className="form-actions">
          <button type="submit" className="submit-button" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Record Vital Signs'}
          </button>
        </div>
      </form>
      
      {previousReadings.length > 0 && (
        <div className="previous-readings-section">
          <button 
            className="toggle-readings-button"
            onClick={togglePreviousReadings}
          >
            {showPreviousReadings ? 'Hide Previous Readings' : 'Show Previous Readings'}
          </button>
          
          {showPreviousReadings && (
            <div className="previous-readings">
              <h3>Previous Vital Signs</h3>
              <table className="readings-table">
                <thead>
                  <tr>
                    <th>Date/Time</th>
                    <th>Temp</th>
                    <th>BP</th>
                    <th>Pulse</th>
                    <th>Resp</th>
                    <th>O₂ Sat</th>
                    <th>Weight</th>
                    <th>Height</th>
                    <th>BMI</th>
                    <th>Recorded By</th>
                  </tr>
                </thead>
                <tbody>
                  {previousReadings.map((reading, index) => (
                    <tr key={index}>
                      <td>{reading.date} {reading.time}</td>
                      <td>{reading.temperature}°C</td>
                      <td>{reading.bloodPressureSystolic}/{reading.bloodPressureDiastolic}</td>
                      <td>{reading.pulseRate}</td>
                      <td>{reading.respiratoryRate}</td>
                      <td>{reading.oxygenSaturation}%</td>
                      <td>{reading.weight} kg</td>
                      <td>{reading.height} cm</td>
                      <td>{reading.bmi}</td>
                      <td>{reading.recordedBy?.rank} {reading.recordedBy?.name}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VitalSigns;
