import React, { useState, useEffect } from 'react';
import '../../styles/MedicalStaffDashboard.css';
import VitalSigns from '../VitalSigns';
import Prescription from '../Prescription';

const MedicalStaffDashboard = ({ user }) => {
  const [activeTab, setActiveTab] = useState('consultations');
  const [consultationRequests, setConsultationRequests] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patientRecords, setPatientRecords] = useState(null);
  const [isNurse, setIsNurse] = useState(false);
  const [isDoctor, setIsDoctor] = useState(false);
  const [signature, setSignature] = useState('');

  useEffect(() => {
    // Determine user role
    const userRole = user?.role || '';
    setIsNurse(userRole === 'nurse' || userRole === 'officer');
    setIsDoctor(userRole === 'doctor' || userRole === 'officer');
    
    // Load signature from localStorage
    const savedSignature = localStorage.getItem(`signature_${user?.id}`);
    if (savedSignature) {
      setSignature(savedSignature);
    }
    
    // Load all consultation requests
    loadAllConsultationRequests();
  }, [user]);

  const loadAllConsultationRequests = () => {
    // In a real app, this would be an API call to get all consultation requests
    // For demo purposes, we'll scan localStorage for all users
    const allRequests = [];
    
    // Get all users
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const demoUsers = [
      { id: 1 },
      { id: 2 },
      { id: 3 },
      { id: 4 },
      { id: 5 }
    ];
    
    const allUsers = [...users, ...demoUsers];
    
    // Get consultation requests for each user
    allUsers.forEach(user => {
      const userRequests = JSON.parse(localStorage.getItem(`consultationRequests_${user.id}`) || '[]');
      allRequests.push(...userRequests);
    });
    
    // Sort by date (newest first)
    allRequests.sort((a, b) => new Date(b.requestDate) - new Date(a.requestDate));
    
    setConsultationRequests(allRequests);
  };

  const handlePatientSelect = (patientId) => {
    // Find the patient in the users list
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const demoUsers = [
      { 
        id: 1, 
        name: "SLt. Guest Officer",
        rank: "Sub Lieutenant",
        serviceNumber: "NN/2023/78945",
        email: "<EMAIL>"
      },
      { 
        id: 2, 
        name: "Cdr. Guest Commander",
        rank: "Commander",
        serviceNumber: "NN/2021/45678",
        email: "<EMAIL>"
      },
      { 
        id: 3, 
        name: "CPO Guest Rating",
        rank: "Chief Petty Officer",
        serviceNumber: "NN/2022/98765",
        email: "<EMAIL>"
      },
      { 
        id: 4, 
        name: "Guest User",
        rank: "Civilian",
        serviceNumber: "GUEST-2024-00001",
        email: "<EMAIL>"
      },
      { 
        id: 5, 
        name: "RAdm OO FADEYI",
        rank: "Rear Admiral",
        serviceNumber: "NN/FOC/2024",
        email: "<EMAIL>"
      }
    ];
    
    const allUsers = [...users, ...demoUsers];
    const patient = allUsers.find(u => u.id === patientId);
    
    if (patient) {
      setSelectedPatient(patient);
      
      // Load patient records
      const vitalSigns = JSON.parse(localStorage.getItem(`vitalSigns_${patientId}`) || '[]');
      const prescriptions = JSON.parse(localStorage.getItem(`prescriptions_${patientId}`) || '[]');
      const consultations = JSON.parse(localStorage.getItem(`consultationRequests_${patientId}`) || '[]');
      
      setPatientRecords({
        vitalSigns,
        prescriptions,
        consultations
      });
    }
  };

  const handleApproveRequest = (request) => {
    // Update the request status to approved
    const updatedRequest = {
      ...request,
      status: 'approved',
      approvedBy: {
        id: user.id,
        name: user.name,
        rank: user.rank
      },
      approvedDate: new Date().toISOString(),
      accessPermissions: {
        nurse: {
          canTakeVitalSigns: true,
          canViewRecords: false
        },
        doctor: {
          canViewRecords: true,
          canPrescribe: true
        }
      }
    };
    
    // Update in localStorage
    const patientRequests = JSON.parse(localStorage.getItem(`consultationRequests_${request.patientId}`) || '[]');
    const updatedRequests = patientRequests.map(req => 
      req.requestId === request.requestId ? updatedRequest : req
    );
    
    localStorage.setItem(`consultationRequests_${request.patientId}`, JSON.stringify(updatedRequests));
    
    // Update the state
    setConsultationRequests(prevRequests => 
      prevRequests.map(req => req.requestId === request.requestId ? updatedRequest : req)
    );
    
    alert(`Consultation request ${request.requestId} has been approved.`);
  };

  const handleCompleteConsultation = (request) => {
    // Update the request status to completed
    const updatedRequest = {
      ...request,
      status: 'completed',
      completedBy: {
        id: user.id,
        name: user.name,
        rank: user.rank
      },
      completedDate: new Date().toISOString(),
      diagnosis: prompt('Enter diagnosis:') || 'General checkup'
    };
    
    // Update in localStorage
    const patientRequests = JSON.parse(localStorage.getItem(`consultationRequests_${request.patientId}`) || '[]');
    const updatedRequests = patientRequests.map(req => 
      req.requestId === request.requestId ? updatedRequest : req
    );
    
    localStorage.setItem(`consultationRequests_${request.patientId}`, JSON.stringify(updatedRequests));
    
    // Update the state
    setConsultationRequests(prevRequests => 
      prevRequests.map(req => req.requestId === request.requestId ? updatedRequest : req)
    );
    
    alert(`Consultation ${request.requestId} has been marked as completed.`);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'status-badge pending';
      case 'approved':
        return 'status-badge approved';
      case 'completed':
        return 'status-badge completed';
      case 'cancelled':
        return 'status-badge cancelled';
      default:
        return 'status-badge';
    }
  };

  const renderConsultationsTab = () => {
    const pendingRequests = consultationRequests.filter(req => req.status === 'pending');
    const approvedRequests = consultationRequests.filter(req => req.status === 'approved');
    const completedRequests = consultationRequests.filter(req => req.status === 'completed');
    
    return (
      <div className="consultations-tab">
        <h3>Pending Consultation Requests</h3>
        {pendingRequests.length === 0 ? (
          <p className="no-data-message">No pending consultation requests.</p>
        ) : (
          <div className="request-list">
            {pendingRequests.map((request, index) => (
              <div key={index} className="request-card">
                <div className="request-header">
                  <span className={getStatusBadgeClass(request.status)}>
                    {request.status.toUpperCase()}
                  </span>
                  <span className="request-id">{request.requestId}</span>
                </div>
                <div className="request-patient">
                  <strong>Patient:</strong> {request.patientName} ({request.patientId})
                </div>
                <div className="request-details">
                  <p><strong>Reason:</strong> {request.reason}</p>
                  <p><strong>Preferred Date:</strong> {formatDate(request.preferredDate)}</p>
                  <p><strong>Urgency:</strong> {request.urgency}</p>
                  <div className="request-symptoms">
                    <strong>Symptoms:</strong>
                    <p>{request.symptoms}</p>
                  </div>
                </div>
                <div className="request-actions">
                  <button 
                    className="view-patient-btn"
                    onClick={() => handlePatientSelect(request.patientId)}
                  >
                    View Patient
                  </button>
                  {isDoctor && (
                    <button 
                      className="approve-btn"
                      onClick={() => handleApproveRequest(request)}
                    >
                      Approve Request
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        <h3>Approved Consultations</h3>
        {approvedRequests.length === 0 ? (
          <p className="no-data-message">No approved consultations.</p>
        ) : (
          <div className="request-list">
            {approvedRequests.map((request, index) => (
              <div key={index} className="request-card">
                <div className="request-header">
                  <span className={getStatusBadgeClass(request.status)}>
                    {request.status.toUpperCase()}
                  </span>
                  <span className="request-id">{request.requestId}</span>
                </div>
                <div className="request-patient">
                  <strong>Patient:</strong> {request.patientName} ({request.patientId})
                </div>
                <div className="request-details">
                  <p><strong>Reason:</strong> {request.reason}</p>
                  <p><strong>Appointment Date:</strong> {formatDate(request.appointmentDate || request.preferredDate)}</p>
                  <p><strong>Approved By:</strong> {request.approvedBy?.rank} {request.approvedBy?.name}</p>
                </div>
                <div className="request-actions">
                  <button 
                    className="view-patient-btn"
                    onClick={() => handlePatientSelect(request.patientId)}
                  >
                    View Patient
                  </button>
                  {isDoctor && (
                    <button 
                      className="complete-btn"
                      onClick={() => handleCompleteConsultation(request)}
                    >
                      Complete Consultation
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        <h3>Recent Completed Consultations</h3>
        {completedRequests.length === 0 ? (
          <p className="no-data-message">No completed consultations.</p>
        ) : (
          <div className="request-list">
            {completedRequests.slice(0, 5).map((request, index) => (
              <div key={index} className="request-card completed-card">
                <div className="request-header">
                  <span className={getStatusBadgeClass(request.status)}>
                    {request.status.toUpperCase()}
                  </span>
                  <span className="request-id">{request.requestId}</span>
                </div>
                <div className="request-patient">
                  <strong>Patient:</strong> {request.patientName} ({request.patientId})
                </div>
                <div className="request-details">
                  <p><strong>Reason:</strong> {request.reason}</p>
                  <p><strong>Completed Date:</strong> {formatDate(request.completedDate)}</p>
                  <p><strong>Diagnosis:</strong> {request.diagnosis || 'Not provided'}</p>
                </div>
                <div className="request-actions">
                  <button 
                    className="view-patient-btn"
                    onClick={() => handlePatientSelect(request.patientId)}
                  >
                    View Patient Records
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderPatientRecordsTab = () => {
    if (!selectedPatient) {
      return (
        <div className="patient-records-tab">
          <div className="select-patient-message">
            <p>Please select a patient from the Consultations tab to view their records.</p>
          </div>
        </div>
      );
    }
    
    return (
      <div className="patient-records-tab">
        <div className="patient-info-card">
          <h3>Patient Information</h3>
          <div className="patient-details">
            <p><strong>Name:</strong> {selectedPatient.name}</p>
            <p><strong>Rank:</strong> {selectedPatient.rank}</p>
            <p><strong>Service Number:</strong> {selectedPatient.serviceNumber}</p>
            <p><strong>Email:</strong> {selectedPatient.email}</p>
          </div>
        </div>
        
        {isDoctor && patientRecords && (
          <>
            <div className="patient-records-section">
              <h3>Vital Signs History</h3>
              {patientRecords.vitalSigns.length === 0 ? (
                <p className="no-data-message">No vital signs records available.</p>
              ) : (
                <table className="records-table">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Temp</th>
                      <th>BP</th>
                      <th>Pulse</th>
                      <th>Resp</th>
                      <th>O₂ Sat</th>
                      <th>Weight</th>
                      <th>BMI</th>
                      <th>Recorded By</th>
                    </tr>
                  </thead>
                  <tbody>
                    {patientRecords.vitalSigns.map((record, index) => (
                      <tr key={index}>
                        <td>{record.date}</td>
                        <td>{record.temperature}°C</td>
                        <td>{record.bloodPressureSystolic}/{record.bloodPressureDiastolic}</td>
                        <td>{record.pulseRate}</td>
                        <td>{record.respiratoryRate}</td>
                        <td>{record.oxygenSaturation}%</td>
                        <td>{record.weight} kg</td>
                        <td>{record.bmi}</td>
                        <td>{record.recordedBy?.rank} {record.recordedBy?.name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            
            <div className="patient-records-section">
              <h3>Prescription History</h3>
              {patientRecords.prescriptions.length === 0 ? (
                <p className="no-data-message">No prescription records available.</p>
              ) : (
                <div className="prescriptions-list">
                  {patientRecords.prescriptions.map((prescription, index) => (
                    <div key={index} className="prescription-card">
                      <div className="prescription-header">
                        <span className="prescription-date">{formatDate(prescription.date)}</span>
                        <span className="prescription-doctor">By: {prescription.prescribedBy?.rank} {prescription.prescribedBy?.name}</span>
                      </div>
                      <div className="prescription-details">
                        <p><strong>Diagnosis:</strong> {prescription.diagnosis}</p>
                        <div className="prescription-medications">
                          <strong>Medications:</strong>
                          <ul>
                            {prescription.medications.map((med, medIndex) => (
                              <li key={medIndex}>
                                <strong>{med.name}</strong> - {med.dosage}, {med.frequency}, for {med.duration}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
        
        {isNurse && (
          <div className="vital-signs-section">
            <h3>Record Vital Signs</h3>
            <VitalSigns patientId={selectedPatient.id} />
          </div>
        )}
        
        {isDoctor && (
          <div className="prescription-section">
            <h3>Create Prescription</h3>
            <Prescription patientId={selectedPatient.id} patientName={selectedPatient.name} />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="medical-staff-dashboard">
      <div className="dashboard-header">
        <h2>Medical Staff Dashboard</h2>
        <div className="staff-info">
          <p>{user.rank} {user.name}</p>
          <p className="staff-role">{isDoctor ? 'Medical Officer' : 'Nurse'}</p>
        </div>
      </div>
      
      <div className="dashboard-tabs">
        <button 
          className={`tab-button ${activeTab === 'consultations' ? 'active' : ''}`}
          onClick={() => setActiveTab('consultations')}
        >
          Consultations
        </button>
        <button 
          className={`tab-button ${activeTab === 'patient-records' ? 'active' : ''}`}
          onClick={() => setActiveTab('patient-records')}
        >
          Patient Records
        </button>
      </div>
      
      <div className="dashboard-content">
        {activeTab === 'consultations' && renderConsultationsTab()}
        {activeTab === 'patient-records' && renderPatientRecordsTab()}
      </div>
    </div>
  );
};

export default MedicalStaffDashboard;
