import React, { useState, useEffect } from "react";
import "./styles/App.css";
import Login from "./components/auth/Login.jsx";
import SignUp from "./components/auth/SignUp.jsx";
import UserDashboard from "./components/dashboard/UserDashboard.jsx";
import MedicalStaffDashboard from "./components/dashboard/MedicalStaffDashboard.jsx";
import MedBlog from "./components/MedBlog.jsx";
import AboutUs from "./components/AboutUs.jsx";
import ProfileView from "./components/ProfileView.jsx";
import UserProfileDropdown from "./components/UserProfileDropdown.jsx";

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [currentView, setCurrentView] = useState("dashboard");
  const [showSignUp, setShowSignUp] = useState(false);

  // Check if personnel is already logged in
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      const personnelId = localStorage.getItem("personnelId");
      const personnelName = localStorage.getItem("personnelName");
      const personnelRank = localStorage.getItem("personnelRank");
      const personnelBranch = localStorage.getItem("personnelBranch");
      const personnelRole = localStorage.getItem("personnelRole");
      const personnelAvatar = localStorage.getItem("personnelAvatar");
      const personnelServiceNumber = localStorage.getItem("personnelServiceNumber");

      if (personnelName) {
        setCurrentUser({
          id: personnelId,
          name: personnelName,
          rank: personnelRank,
          branch: personnelBranch,
          role: personnelRole,
          avatar: personnelAvatar,
          serviceNumber: personnelServiceNumber
        });
        setIsLoggedIn(true);
      }
    }
  }, []);

  // Login handler
  const handleLogin = (personnel) => {
    setCurrentUser(personnel);
    setIsLoggedIn(true);
    setCurrentView("dashboard");
  };

  // Logout handler
  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("personnelId");
    localStorage.removeItem("personnelName");
    localStorage.removeItem("personnelRank");
    localStorage.removeItem("personnelBranch");
    localStorage.removeItem("personnelRole");
    localStorage.removeItem("personnelAvatar");
    localStorage.removeItem("personnelServiceNumber");
    setIsLoggedIn(false);
    setCurrentUser(null);
  };

  // Navigation handler
  const handleNavigation = (view) => {
    setCurrentView(view);
  };

  // Render the current view
  const renderView = () => {
    // Check if personnel is medical staff (doctor or nurse)
    const isMedicalStaff = currentUser && (
      currentUser.role === "doctor" || 
      currentUser.role === "nurse" ||
      (currentUser.role === "officer" && currentUser.rank &&
        (currentUser.rank.includes("Doctor") || currentUser.rank.includes("Medical")))
    );

    switch(currentView) {
      case "dashboard":
        return isMedicalStaff ?
          <MedicalStaffDashboard personnel={currentUser} /> : 
          <UserDashboard personnel={currentUser} />;
      case "profile":
        return <ProfileView personnel={currentUser} />;
      case "medblog":
        return <MedBlog mode="full" />;
      case "about":
        return <AboutUs />;
      default:
        return isMedicalStaff ?
          <MedicalStaffDashboard personnel={currentUser} /> :
          <UserDashboard personnel={currentUser} />;
    }
  };

  return (
    <div className="app">
      {showSignUp ? (
        <SignUp onBackToLogin={() => setShowSignUp(false)} />
      ) : isLoggedIn ? (
        <>
          <header className="app-header">
            <div className="header-left">
              <h1>Logistics Command Medical Portal</h1>
            </div>
            <nav className="main-nav">
              <button
                className={`nav-button ${currentView === "dashboard" ? "active" : ""}`}
                onClick={() => handleNavigation("dashboard")}
              >
                Dashboard
              </button>
              <button
                className={`nav-button ${currentView === "medblog" ? "active" : ""}`}
                onClick={() => handleNavigation("medblog")}
              >
                Medical Blog
              </button>
              <button
                className={`nav-button ${currentView === "about" ? "active" : ""}`}
                onClick={() => handleNavigation("about")}
              >
                About Us
              </button>
            </nav>
            <div className="header-right">
              {currentUser && (
                <UserProfileDropdown
                  personnel={currentUser}
                  onLogout={handleLogout}
                  onNavigate={handleNavigation}
                />
              )}
            </div>
          </header>

          <div className="app-content">
            {renderView()}
          </div>
        </>
      ) : (
        <Login onLoginSuccess={handleLogin} onSignUpClick={() => setShowSignUp(true)} />
      )}
    </div>
  );
}

export default App;
