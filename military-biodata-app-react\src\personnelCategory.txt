﻿              <label htmlFor="personnelCategory">Personnel Category*</label>
              <select
                id="personnelCategory"
                name="personnelCategory"
                value={formData.personnelCategory}
                onChange={handleChange}
              >
                <option value="">Select Category</option>
                <option value="Commissioned Officer">Commissioned Officer</option>
                {formData.service === "Navy" && <option value="Rating">Rating</option>}
                {formData.service === "Army" && <option value="Soldier">Soldier</option>}
                {formData.service === "AirForce" && <option value="Airman/Airwoman">Airman/Airwoman</option>}
              </select>
              {errors.personnelCategory && <div className="error-message">{errors.personnelCategory}</div>}
