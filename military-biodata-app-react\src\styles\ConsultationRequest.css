.consultation-request-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.consultation-request-container h2 {
  color: #003366;
  margin-bottom: 1rem;
  border-bottom: 2px solid #003366;
  padding-bottom: 0.5rem;
}

.form-description {
  margin-bottom: 1.5rem;
  color: #555;
  line-height: 1.5;
}

.consultation-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1.5rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #003366;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.2);
}

.error-message {
  color: #d32f2f;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #003366;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #004080;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.active-requests {
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}

.active-requests h3 {
  color: #003366;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.request-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eee;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #fff4e5;
  color: #ff9800;
  border: 1px solid #ff9800;
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.status-badge.completed {
  background-color: #e3f2fd;
  color: #2196f3;
  border: 1px solid #2196f3;
}

.status-badge.cancelled {
  background-color: #ffebee;
  color: #f44336;
  border: 1px solid #f44336;
}

.request-id {
  font-size: 0.9rem;
  color: #666;
}

.request-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.request-details p {
  margin: 0;
}

.request-symptoms,
.request-notes,
.request-diagnosis {
  margin-top: 0.5rem;
}

.request-symptoms p,
.request-notes p,
.request-diagnosis p {
  margin-top: 0.25rem;
  padding: 0.75rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #003366;
}

.request-footer {
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
  text-align: right;
}

.request-history {
  margin-top: 2rem;
  border-top: 1px solid #eee;
  padding-top: 1.5rem;
}

.toggle-history-button {
  background-color: transparent;
  color: #003366;
  border: 1px solid #003366;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-history-button:hover {
  background-color: #f0f7ff;
}

.history-list {
  margin-top: 1.5rem;
}

.history-list h3 {
  color: #003366;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.history-card {
  opacity: 0.85;
}

.new-request-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.new-request-button {
  background-color: #003366;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-request-button:hover {
  background-color: #004080;
}

.unauthorized-message {
  background-color: #fff4e5;
  border-left: 4px solid #ff9800;
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 4px;
}

.unauthorized-message p {
  margin: 0.5rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .request-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
