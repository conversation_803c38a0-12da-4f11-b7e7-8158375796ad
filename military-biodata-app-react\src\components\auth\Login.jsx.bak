import React, { useState } from "react";
import "./login.css";

// Sample user data with proper Naval ranks
const users = [
  {
    id: 1,
    email: "<EMAIL>",
    name: "SLt. Guest Officer",
    rank: "Sub Lieutenant",
    serviceNumber: "NN/2023/78945",
    branch: "Navy",
    division: "Logistics Command",
    role: "officer",
    avatar: "/images/logos/NN.png"
  },
  {
    id: 2,
    email: "<EMAIL>",
    name: "Cdr. Guest Commander",
    rank: "Commander",
    serviceNumber: "NN/2021/45678",
    branch: "Navy",
    division: "Logistics Command",
    role: "officer",
    avatar: "/images/logos/NN.png"
  },
  {
    id: 3,
    email: "<EMAIL>",
    name: "CPO Guest Rating",
    rank: "Chief Petty Officer",
    serviceNumber: "NN/2022/98765",
    branch: "Navy",
    division: "Logistics Command",
    role: "enlisted",
    avatar: "/images/logos/NN.png"
  },
  {
    id: 4,
    email: "<EMAIL>",
    name: "Guest User",
    rank: "Civilian",
    serviceNumber: "GUEST-2024-00001",
    branch: "Civilian",
    division: "Guest Access",
    role: "guest",
    avatar: "/images/logos/NN.png"
  },
  {
    id: 5,
    email: "<EMAIL>",
    name: "RAdm OO FADEYI",
    rank: "Rear Admiral",
    serviceNumber: "NN/FOC/2024",
    branch: "Navy",
    division: "Logistics Command",
    role: "officer",
    avatar: "/images/logos/NN.png"
  }
];

const Login = ({ onLoginSuccess, onSignUpClick }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const validateEmail = (email) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateEmail(email)) {
      return setError("Please enter a valid email address.");
    }

    if (!password) {
      return setError("Please enter your password.");
    }

    setLoading(true);
    setError("");

    try {
      // Check for registered users in localStorage
      const registeredUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const allUsers = [...users, ...registeredUsers];

      // Find the user with the matching email
      let user = allUsers.find(u => u.email === email);

      // FOR DEMO PURPOSES: If user not found, create a demo user
      if (!user) {
        user = {
          id: Date.now(),
          email: email,
          name: "Demo User",
          rank: "Lieutenant",
          serviceNumber: "NN/DEMO/2024",
          branch: "Navy",
          division: "Logistics Command",
          role: "officer",
          avatar: "/images/logos/NN.png"
        };
      }

      // Simulate successful login
      localStorage.setItem("token", "demo-token-123");
      localStorage.setItem("userId", user.id);
      localStorage.setItem("userName", user.name);
      localStorage.setItem("userRank", user.rank);
      localStorage.setItem("userBranch", user.branch);
      localStorage.setItem("userRole", user.role);
      localStorage.setItem("userAvatar", user.avatar);
      localStorage.setItem("userServiceNumber", user.serviceNumber);

      // Call the onLoginSuccess callback from parent
      if (onLoginSuccess) {
        onLoginSuccess(user);
      }
    } catch (err) {
      setError("❌ Login failed. Please check your credentials.");
    } finally {
      setLoading(false);
    }
  };

  // State for guest view
  const [guestView, setGuestView] = useState(null);

  const handleBlogClick = () => {
    setGuestView('medblog');
  };

  const handleAboutUsClick = () => {
    setGuestView('about');
  };

  const handleBackToLogin = () => {
    setGuestView(null);
  };

  // Import the necessary components
  const MedBlog = React.lazy(() => import('../MedBlog'));
  const AboutUs = React.lazy(<?php
/**
 * This function calculates the sum of two numbers.
 *
 * @param float $a The first number to be added.
 * @param float $b The second number to be added.
 * @return float The sum of the two numbers.
 */
function add($a, $b) {
    return $a + $b;
}
?>);

  // Render based on the guest view
  if (guestView === 'medblog') {
    return (
      <div className="login-container">
        <div className="login-nav">
          <button className="nav-button" onClick={handleBackToLogin}>Back to Login</button>
        </div>
        <div className="guest-view-container">
          <React.Suspense fallback={<div>Loading...</div>}>
            <MedBlog />
          </React.Suspense>
        </div>
      </div>
    );
  }

  if (guestView === 'about') {
    return (
      <div className="login-container">
        <div className="login-nav">
          <button className="nav-button" onClick={handleBackToLogin}>Back to Login</button>
        </div>
        <div className="guest-view-container">
          <React.Suspense fallback={<div>Loading...</div>}>
            <AboutUs />
          </React.Suspense>
        </div>
      </div>
    );
  }

  // Default login view
  return (
    <div className="login-container">
      <div className="login-nav">
        <button className="nav-button" onClick={handleBlogClick}>Medical Blog</button>
        <button className="nav-button" onClick={handleAboutUsClick}>About Us</button>
      </div>

      <div className="login-header">
        <img src="/images/logos/HQLC.png" alt="Logistics Command Logo" className="login-logo" />
        <h1>Logistics Command Medical Portal</h1>
        <p>Welcome to the Nigerian Navy Logistics Command Medical Portal. Access your comprehensive medical records, schedule appointments, track medications, and manage your health information securely in one place.</p>
      </div>



      <form onSubmit={handleSubmit} className="login-form">
        <h2 className="login-title">Personnel Login</h2>
        <p className="login-subtitle">Enter your credentials to access your secure medical portal</p>
        {error && <p className="login-error">{error}</p>}

        <label>Email Address</label>
        <input
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />

        <label>Password</label>
        <input
          type="password"
          placeholder="Enter your password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />

        <div className="form-footer">
          <p className="password-hint">For demo purposes, any password will work</p>
          <button type="submit" className="login-button" disabled={loading}>
            {loading ? "Logging in..." : "Login"}
          </button>
        </div>

        <div className="signup-link">
          <p>Don't have an account? <button onClick={onSignUpClick} className="signup-link-button">Register Now</button></p>
        </div>
      </form>
    </div>
  );
};

export default Login;
