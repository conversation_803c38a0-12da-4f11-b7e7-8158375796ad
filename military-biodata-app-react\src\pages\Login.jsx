import React, { useState } from 'react';
import api from '../api'; // Importing the API client for making requests
import { useNavigate } from 'react-router-dom'; // Importing the useNavigate hook for navigation

/**
 * Login component that handles user authentication.
 * It allows users to log in using their service number and password.
 * 
 * @param {Object} props - The component props.
 * @param {Function} props.setUser - Function to update the user state in the parent component.
 * @returns {JSX.Element} The rendered Login component.
 */
const Login = ({ setUser }) => {
  // State variables for managing input values and loading/error states
  const [serviceNo, setServiceNo] = useState(''); // Service number input
  const [password, setPassword] = useState(''); // Password input
  const [loading, setLoading] = useState(false); // Loading state for the login process
  const [error, setError] = useState(''); // Error message state
  const nav = useNavigate(); // Hook for programmatic navigation

  /**
   * Handles the login process when the user clicks the login button.
   * Validates input, makes an API call to authenticate the user,
   * and handles the response.
   */
  const login = () => {
    // Validate input fields
    if (!serviceNo || !password) {
      setError('Please enter both service number and password.');
      return; // Exit if validation fails
    }
    
    setLoading(true); // Set loading state to true
    setError(''); // Clear previous errors

    // Make a POST request to the login endpoint
    api.post('/auth/login', { serviceNo, password })
      .then(res => {
        // Store the authentication token in local storage
        localStorage.setItem('token', res.data.token);
        
        // Store the user role in local storage
        localStorage.setItem('role', res.data.role);
        
        // Update the user state with the new role
        setUser({ role: res.data.role });
        
        // Navigate to the dashboard upon successful login
        nav('/dashboard');
      })
      .catch(err => {
        // Handle errors by setting the error message
        setError(err.response?.data?.error || 'Login failed');
      })
      .finally(() => {
        // Reset loading state regardless of success or failure
        setLoading(false);
      });
  };

  return (
    <div className="auth-form">
      <h2>🔐 Login</h2>
      {error && <p className="error-message">{error}</p>} {/* Display error message if exists */}
      <input 
        value={serviceNo} 
        onChange={e => setServiceNo(e.target.value)} 
        placeholder="Service Number" 
      />
      <input 
        type="password" 
        value={password} 
        onChange={e => setPassword(e.target.value)} 
        placeholder="Password" 
      />
      <button onClick={login} disabled={loading}>
        {loading ? 'Logging in...' : 'Login'} {/* Change button text based on loading state */}
      </button>
    </div>
  );
};

export default Login;

/**
 * This code defines a Login component that allows users to log in using their service number and password.
 * It uses the `useState` hook to manage the input values and loading state, and the `useNavigate` hook from React Router for navigation.
 */
