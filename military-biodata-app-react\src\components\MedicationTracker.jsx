import React, { useState, useEffect } from 'react';
import '../styles/MedicationTracker.css';

const MedicationTracker = ({ user }) => {
  // State for medications
  const [medications, setMedications] = useState([]);
  const [showForm, setShowForm] = useState(false);
  
  // Form state
  const [medicationName, setMedicationName] = useState('');
  const [dosage, setDosage] = useState('');
  const [frequency, setFrequency] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [instructions, setInstructions] = useState('');
  const [prescribedBy, setPrescribedBy] = useState('');
  
  // Frequency options
  const frequencyOptions = [
    'Once daily',
    'Twice daily',
    'Three times daily',
    'Four times daily',
    'Every morning',
    'Every evening',
    'Every 4 hours',
    'Every 6 hours',
    'Every 8 hours',
    'Every 12 hours',
    'As needed'
  ];
  
  // Load user-specific medications
  useEffect(() => {
    if (user) {
      // In a real app, this would be an API call
      // For demo purposes, we'll use mock data based on user ID
      let userMedications = [];
      
      if (user.id === 1) { // John Doe
        userMedications = [
          {
            id: 1,
            name: 'Ibuprofen',
            dosage: '400mg',
            frequency: 'Every 6 hours',
            startDate: '2025-04-10',
            endDate: '2025-04-17',
            instructions: 'Take with food',
            prescribedBy: 'Dr. Emily Chen',
            status: 'Active'
          },
          {
            id: 2,
            name: 'Amoxicillin',
            dosage: '500mg',
            frequency: 'Three times daily',
            startDate: '2025-03-15',
            endDate: '2025-03-25',
            instructions: 'Take until completed',
            prescribedBy: 'Dr. James Wilson',
            status: 'Completed'
          }
        ];
      } else if (user.id === 2) { // Sarah Wilson
        userMedications = [
          {
            id: 3,
            name: 'Loratadine',
            dosage: '10mg',
            frequency: 'Once daily',
            startDate: '2025-04-01',
            endDate: '2025-05-01',
            instructions: 'Take in the morning',
            prescribedBy: 'Dr. Michael Brown',
            status: 'Active'
          }
        ];
      } else if (user.id === 3) { // Michael Johnson
        userMedications = [
          {
            id: 4,
            name: 'Acetaminophen',
            dosage: '500mg',
            frequency: 'Every 6 hours',
            startDate: '2025-04-05',
            endDate: '2025-04-12',
            instructions: 'Do not exceed 4000mg in 24 hours',
            prescribedBy: 'Dr. Sarah Lee',
            status: 'Active'
          },
          {
            id: 5,
            name: 'Ciprofloxacin',
            dosage: '250mg',
            frequency: 'Twice daily',
            startDate: '2025-02-20',
            endDate: '2025-03-05',
            instructions: 'Take with plenty of water',
            prescribedBy: 'Dr. Robert Johnson',
            status: 'Completed'
          }
        ];
      }
      
      setMedications(userMedications);
    }
  }, [user]);
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Create new medication
    const newMedication = {
      id: medications.length + 1,
      name: medicationName,
      dosage: dosage,
      frequency: frequency,
      startDate: startDate,
      endDate: endDate,
      instructions: instructions,
      prescribedBy: prescribedBy,
      status: 'Active'
    };
    
    // Add to medications list
    setMedications([...medications, newMedication]);
    
    // Reset form
    setMedicationName('');
    setDosage('');
    setFrequency('');
    setStartDate('');
    setEndDate('');
    setInstructions('');
    setPrescribedBy('');
    setShowForm(false);
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Get today's date in YYYY-MM-DD format for min date in date picker
  const today = new Date().toISOString().split('T')[0];
  
  return (
    <div className="medication-container">
      <div className="medication-header">
        <h2>Medication Tracker</h2>
        <button 
          className="new-medication-btn"
          onClick={() => setShowForm(!showForm)}
        >
          {showForm ? 'Cancel' : 'Add New Medication'}
        </button>
      </div>
      
      {showForm && (
        <div className="medication-form-container">
          <h3>Add New Medication</h3>
          <form onSubmit={handleSubmit} className="medication-form">
            <div className="form-group">
              <label htmlFor="medication-name">Medication Name</label>
              <input
                type="text"
                id="medication-name"
                value={medicationName}
                onChange={(e) => setMedicationName(e.target.value)}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-dosage">Dosage</label>
              <input
                type="text"
                id="medication-dosage"
                value={dosage}
                onChange={(e) => setDosage(e.target.value)}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-frequency">Frequency</label>
              <select
                id="medication-frequency"
                value={frequency}
                onChange={(e) => setFrequency(e.target.value)}
                required
              >
                <option value="">Select frequency</option>
                {frequencyOptions.map((option, index) => (
                  <option key={index} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-start-date">Start Date</label>
              <input
                type="date"
                id="medication-start-date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-end-date">End Date</label>
              <input
                type="date"
                id="medication-end-date"
                min={startDate || today}
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-instructions">Instructions</label>
              <textarea
                id="medication-instructions"
                value={instructions}
                onChange={(e) => setInstructions(e.target.value)}
                placeholder="Special instructions for taking this medication"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="medication-prescribed-by">Prescribed By</label>
              <input
                type="text"
                id="medication-prescribed-by"
                value={prescribedBy}
                onChange={(e) => setPrescribedBy(e.target.value)}
                required
              />
            </div>
            
            <button type="submit" className="submit-btn">Add Medication</button>
          </form>
        </div>
      )}
      
      <div className="medications-list">
        <div className="medication-tabs">
          <button className="tab-btn active">Active Medications</button>
          <button className="tab-btn">Completed Medications</button>
          <button className="tab-btn">All Medications</button>
        </div>
        
        {medications.length > 0 ? (
          <div className="medication-cards">
            {medications.filter(med => med.status === 'Active').map((medication) => (
              <div key={medication.id} className="medication-card">
                <div className="medication-card-header">
                  <span className="medication-name">{medication.name}</span>
                  <span className={`medication-status ${medication.status.toLowerCase()}`}>
                    {medication.status}
                  </span>
                </div>
                <div className="medication-card-body">
                  <div className="medication-detail">
                    <span className="detail-label">Dosage:</span>
                    <span className="detail-value">{medication.dosage}</span>
                  </div>
                  <div className="medication-detail">
                    <span className="detail-label">Frequency:</span>
                    <span className="detail-value">{medication.frequency}</span>
                  </div>
                  <div className="medication-detail">
                    <span className="detail-label">Start Date:</span>
                    <span className="detail-value">{formatDate(medication.startDate)}</span>
                  </div>
                  <div className="medication-detail">
                    <span className="detail-label">End Date:</span>
                    <span className="detail-value">
                      {medication.endDate ? formatDate(medication.endDate) : 'Ongoing'}
                    </span>
                  </div>
                  {medication.instructions && (
                    <div className="medication-detail">
                      <span className="detail-label">Instructions:</span>
                      <span className="detail-value">{medication.instructions}</span>
                    </div>
                  )}
                  <div className="medication-detail">
                    <span className="detail-label">Prescribed By:</span>
                    <span className="detail-value">{medication.prescribedBy}</span>
                  </div>
                </div>
                <div className="medication-card-footer">
                  <button className="refill-btn">Request Refill</button>
                  <button className="mark-btn">Mark as Completed</button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="no-medications">No medications to display.</p>
        )}
      </div>
    </div>
  );
};

export default MedicationTracker;
