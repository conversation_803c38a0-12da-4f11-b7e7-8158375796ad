import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaUserCircle, FaBell, FaCog, FaSignOutAlt, FaChartLine, FaUserMd, FaNewspaper, FaShieldAlt } from 'react-icons/fa';
import './Navbar.css';

const Navbar = () => {
    const navigate = useNavigate();
    const isLoggedIn = localStorage.getItem('token');
    const userRole = localStorage.getItem('role');
    const [notifications] = useState([
        { id: 1, text: "Your annual checkup is due in 7 days", date: "2023-06-20" },
        { id: 2, text: "New health advisory posted", date: "2023-06-18" }
    ]);
    const [showNotifications, setShowNotifications] = useState(false);
    const [showUserMenu, setShowUserMenu] = useState(false);

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('role');
        navigate('/login');
    };

    return (
        <nav className="navbar">
            <div className="navbar-brand">
                <FaShieldAlt className="brand-icon" />
                <span>NavMed Tracker</span>
            </div>
            <div className="nav-content">
                <div className="breadcrumb">
                    {window.location.pathname.split('/').map((path, i) => (
                        path && <span key={i}> / {path}</span>
                    ))}
                </div>
                <ul className="nav-links">
                    {isLoggedIn ? (
                        <>
                            <li><Link to="/dashboard"><FaChartLine /> Dashboard</Link></li>
                            {userRole === 'doctor' && <li><Link to="/admin"><FaUserMd /> Admin</Link></li>}
                            <li><Link to="/blog"><FaNewspaper /> Health Hub</Link></li>
                            <li className="notifications">
                                <button 
                                    className="notification-button"
                                    onClick={() => setShowNotifications(!showNotifications)}
                                >
                                    <FaBell />
                                    {notifications.length > 0 && (
                                        <span className="notification-badge">{notifications.length}</span>
                                    )}
                                </button>
                                {showNotifications && (
                                    <div className="notification-dropdown">
                                        <h4>Notifications</h4>
                                        {notifications.length > 0 ? (
                                            <ul>
                                                {notifications.map(notification => (
                                                    <li key={notification.id}>
                                                        <p>{notification.text}</p>
                                                        <span>{notification.date}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        ) : (
                                            <p className="no-notifications">No new notifications</p>
                                        )}
                                    </div>
                                )}
                            </li>
                            <li className="user-profile">
                                <button 
                                    className="profile-button"
                                    onClick={() => setShowUserMenu(!showUserMenu)}
                                >
                                    <img 
                                        src={localStorage.getItem('userAvatar') || '/default-avatar.png'} 
                                        alt="Profile" 
                                        className="avatar"
                                        onError={(e) => e.target.src = 'https://via.placeholder.com/32'} 
                                    />
                                    <span className="user-name">{localStorage.getItem('userName') || 'Lt. John Doe'}</span>
                                </button>
                                {showUserMenu && (
                                    <div className="user-dropdown">
                                        <ul>
                                            <li><Link to="/profile"><FaUserCircle /> My Profile</Link></li>
                                            <li><Link to="/settings"><FaCog /> Settings</Link></li>
                                            <li><button onClick={handleLogout}><FaSignOutAlt /> Logout</button></li>
                                        </ul>
                                    </div>
                                )}
                            </li>
                        </>
                    ) : (
                        <>
                            <li><Link to="/login">Login</Link></li>
                            <li><Link to="/register">Register</Link></li>
                        </>
                    )}
                </ul>
            </div>
        </nav>
    );
};

export default Navbar;
