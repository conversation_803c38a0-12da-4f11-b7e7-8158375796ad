.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  font-family: 'Segoe UI', sans-serif;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.profile-photo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1.5rem;
  border: 3px solid #3498db;
}

.profile-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-title h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.profile-title p {
  margin: 0.25rem 0;
  color: #7f8c8d;
}

.profile-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #ddd;
}

.profile-tabs button {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.profile-tabs button:hover {
  background-color: #f5f5f5;
}

.profile-tabs button.active {
  border-bottom: 3px solid #3498db;
  font-weight: bold;
}

.profile-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  padding: 1.5rem;
}

.info-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.info-card h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item label {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 0.25rem;
}

.info-item span {
  font-weight: 500;
  color: #2c3e50;
}

.allergies-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.allergies-list li {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  display: inline-block;
  margin-right: 0.5rem;
}

.medical-table, .checkup-table, .bp-table, .eye-test-table, .weight-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
}

.medical-table th, .checkup-table th, .bp-table th, .eye-test-table th, .weight-table th,
.medical-table td, .checkup-table td, .bp-table td, .eye-test-table td, .weight-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.medical-table th, .checkup-table th, .bp-table th, .eye-test-table th, .weight-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.normal {
  color: green;
  font-weight: bold;
}

.elevated, .overweight {
  color: orange;
  font-weight: bold;
}

.high, .obese {
  color: red;
  font-weight: bold;
}

.underweight {
  color: #3498db;
  font-weight: bold;
}

.completed {
  color: green;
  font-weight: bold;
}

.pending {
  color: orange;
  font-weight: bold;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-photo {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .profile-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .medical-table, .checkup-table, .bp-table, .eye-test-table, .weight-table {
    display: block;
    overflow-x: auto;
  }
}

