import React, { useEffect, useState, useRef } from 'react';
import api from '../api';
import Chart from 'chart.js/auto';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const [stats, setStats] = useState({ bp: [], annuals: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    setLoading(true);
    api.get('/dashboard/stats')
      .then(res => {
        setStats(res.data);
        setLoading(false);
      })
      .catch(err => {
        console.error('Failed to fetch stats:', err);
        setError('Failed to load dashboard data. Please try again later.');
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (stats.bp.length > 0 && chartRef.current) {
      // Destroy existing chart if it exists
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext('2d');
      chartInstance.current = new Chart(ctx, {
        type: 'line',
        data: {
          labels: stats.bp.map(entry => new Date(entry.date).toLocaleDateString()),
          datasets: [
            {
              label: 'Systolic',
              data: stats.bp.map(entry => entry.systolic),
              borderColor: 'rgba(255, 99, 132, 0.7)',
              fill: false,
              tension: 0.4
            },
            {
              label: 'Diastolic',
              data: stats.bp.map(entry => entry.diastolic),
              borderColor: 'rgba(54, 162, 235, 0.7)',
              fill: false,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          plugins: { 
            legend: { position: 'top' },
            tooltip: {
              callbacks: {
                title: function(tooltipItems) {
                  return 'Date: ' + tooltipItems[0].label;
                },
                label: function(context) {
                  return context.dataset.label + ': ' + context.raw + ' mmHg';
                }
              }
            }
          },
          scales: { 
            y: { 
              beginAtZero: false,
              title: {
                display: true,
                text: 'Blood Pressure (mmHg)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Date'
              }
            }
          }
        }
      });
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [stats.bp]);

  if (loading) return <div className="loading">Loading dashboard data...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="dashboard">
      <h2>📊 Health Monitoring Dashboard</h2>
      
      <div className="dashboard-tabs">
        <button 
          className={activeTab === 'overview' ? 'active' : ''} 
          onClick={() => setActiveTab('overview')}>
          Overview
        </button>
        <button 
          className={activeTab === 'bp' ? 'active' : ''} 
          onClick={() => setActiveTab('bp')}>
          Blood Pressure
        </button>
        <button 
          className={activeTab === 'checkups' ? 'active' : ''} 
          onClick={() => setActiveTab('checkups')}>
          Annual Checkups
        </button>
      </div>

      {activeTab === 'overview' && (
        <div className="dashboard-overview">
          <div className="stats-summary">
            <div className="stat-card">
              <h3>Latest BP</h3>
              {stats.bp.length > 0 ? (
                <div className="bp-reading">
                  <span className="systolic">{stats.bp[stats.bp.length-1].systolic}</span>/
                  <span className="diastolic">{stats.bp[stats.bp.length-1].diastolic}</span>
                  <span className="unit">mmHg</span>
                </div>
              ) : (
                <p>No BP records available</p>
              )}
            </div>
            <div className="stat-card">
              <h3>Checkups</h3>
              <p>{stats.annuals.length} recorded</p>
              <p>Latest: {stats.annuals.length > 0 ? new Date(stats.annuals[stats.annuals.length-1].date).toLocaleDateString() : 'None'}</p>
            </div>
          </div>
          <canvas ref={chartRef} height="100"></canvas>
        </div>
      )}

      {activeTab === 'bp' && (
        <div className="bp-history">
          <h3>Blood Pressure History</h3>
          <canvas ref={chartRef} height="100"></canvas>
          <div className="bp-table">
            <table>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Systolic</th>
                  <th>Diastolic</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {stats.bp.map((reading, i) => (
                  <tr key={i}>
                    <td>{new Date(reading.date).toLocaleDateString()}</td>
                    <td>{reading.systolic}</td>
                    <td>{reading.diastolic}</td>
                    <td>
                      {reading.systolic < 120 && reading.diastolic < 80 ? 
                        <span className="normal">Normal</span> : 
                        reading.systolic < 130 && reading.diastolic < 85 ? 
                        <span className="elevated">Elevated</span> : 
                        <span className="high">High</span>}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'checkups' && (
        <section className="checkup-section">
          <h3>🩺 Annual Checkups</h3>
          {stats.annuals.length > 0 ? (
            <div className="checkup-list">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.annuals.map((a, i) => (
                    <tr key={i}>
                      <td>{new Date(a.date).toDateString()}</td>
                      <td><span className={a.status}>{a.status}</span></td>
                      <td>{a.notes || 'No notes available'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p>No annual checkup records available.</p>
          )}
        </section>
      )}
    </div>
  );
};

export default Dashboard;


