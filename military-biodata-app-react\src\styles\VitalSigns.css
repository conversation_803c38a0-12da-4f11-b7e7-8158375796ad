.vital-signs-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.vital-signs-container h2 {
  color: #003366;
  margin-bottom: 1rem;
  border-bottom: 2px solid #003366;
  padding-bottom: 0.5rem;
}

.nurse-info {
  font-weight: 500;
  margin-bottom: 1.5rem;
  padding: 0.5rem;
  background-color: #f0f7ff;
  border-left: 4px solid #003366;
  border-radius: 4px;
}

.vital-signs-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1.5rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #003366;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.2);
}

.form-group input[readonly] {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.blood-pressure .bp-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blood-pressure .bp-inputs input {
  flex: 1;
}

.blood-pressure .bp-inputs span {
  font-weight: bold;
  font-size: 1.2rem;
}

.error-message {
  color: #d32f2f;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #003366;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #004080;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.previous-readings-section {
  margin-top: 2rem;
  border-top: 1px solid #eee;
  padding-top: 1.5rem;
}

.toggle-readings-button {
  background-color: transparent;
  color: #003366;
  border: 1px solid #003366;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-readings-button:hover {
  background-color: #f0f7ff;
}

.previous-readings {
  margin-top: 1.5rem;
}

.previous-readings h3 {
  color: #003366;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.readings-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.readings-table th,
.readings-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.readings-table th {
  background-color: #f0f7ff;
  color: #003366;
  font-weight: 600;
}

.readings-table tr:hover {
  background-color: #f9f9f9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .readings-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}
