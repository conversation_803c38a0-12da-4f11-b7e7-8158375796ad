import React, { useState, useEffect } from "react";
import "../../styles/Dashboard.css";
import AppointmentScheduler from "../AppointmentScheduler";
import MedicationTracker from "../MedicationTracker";
import ConsultationRequest from "../ConsultationRequest";
import MedBlog from "../MedBlog.jsx";

const UserDashboard = ({ user }) => {
  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    medicalHistory: false,
    consultationRequests: true,
    appointments: false,
    medications: false,
    healthHub: true
  });

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Rest of your component code...
