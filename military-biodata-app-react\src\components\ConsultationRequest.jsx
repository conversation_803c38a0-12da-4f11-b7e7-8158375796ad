import React, { useState, useEffect } from 'react';
import '../styles/ConsultationRequest.css';

const ConsultationRequest = ({ user, onRequestSubmitted }) => {
  const [consultationData, setConsultationData] = useState({
    patientId: user?.id || '',
    patientName: user?.name || '',
    medicalCenter: '',
    reason: '',
    preferredDate: '',
    preferredTime: '',
    symptoms: '',
    urgency: 'normal',
    additionalNotes: '',
    status: 'pending'
  });

  const [pendingRequests, setPendingRequests] = useState([]);
  const [approvedRequests, setApprovedRequests] = useState([]);
  const [completedRequests, setCompletedRequests] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [showHistory, setShowHistory] = useState(false);

  // Load existing consultation requests
  useEffect(() => {
    if (user?.id) {
      // In a real app, this would be an API call
      const storedRequests = JSON.parse(localStorage.getItem(`consultationRequests_${user.id}`) || '[]');

      // Filter requests by status
      setPendingRequests(storedRequests.filter(req => req.status === 'pending'));
      setApprovedRequests(storedRequests.filter(req => req.status === 'approved'));
      setCompletedRequests(storedRequests.filter(req => req.status === 'completed'));
    }
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setConsultationData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!consultationData.medicalCenter) {
      newErrors.medicalCenter = 'Please select a medical center';
    }

    if (!consultationData.reason.trim()) {
      newErrors.reason = 'Reason for consultation is required';
    }

    if (!consultationData.preferredDate) {
      newErrors.preferredDate = 'Preferred date is required';
    } else {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(consultationData.preferredDate);
      if (selectedDate < today) {
        newErrors.preferredDate = 'Date cannot be in the past';
      }
    }

    if (!consultationData.preferredTime) {
      newErrors.preferredTime = 'Preferred time is required';
    }

    if (!consultationData.symptoms.trim()) {
      newErrors.symptoms = 'Symptoms description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      // Create a new consultation request
      const newRequest = {
        ...consultationData,
        requestId: `REQ-${Date.now()}`,
        requestDate: new Date().toISOString(),
        status: 'pending',
        accessPermissions: {
          nurse: {
            canTakeVitalSigns: true,
            canViewRecords: false
          },
          doctor: {
            canViewRecords: false, // Will be set to true when approved
            canPrescribe: false // Will be set to true when approved
          }
        }
      };

      // In a real app, this would be an API call
      setTimeout(() => {
        // Get existing requests
        const existingRequests = JSON.parse(localStorage.getItem(`consultationRequests_${user.id}`) || '[]');

        // Add new request
        const updatedRequests = [newRequest, ...existingRequests];
        localStorage.setItem(`consultationRequests_${user.id}`, JSON.stringify(updatedRequests));

        // Update state
        setPendingRequests([newRequest, ...pendingRequests]);

        // Reset form
        setConsultationData({
          patientId: user?.id || '',
          patientName: user?.name || '',
          reason: '',
          preferredDate: '',
          preferredTime: '',
          symptoms: '',
          urgency: 'normal',
          additionalNotes: '',
          status: 'pending'
        });

        setIsSubmitting(false);

        // Notify parent component
        if (onRequestSubmitted) {
          onRequestSubmitted(newRequest);
        }

        alert('Consultation request submitted successfully!');
      }, 1000);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString) => {
    return timeString;
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'status-badge pending';
      case 'approved':
        return 'status-badge approved';
      case 'completed':
        return 'status-badge completed';
      case 'cancelled':
        return 'status-badge cancelled';
      default:
        return 'status-badge';
    }
  };

  const toggleHistory = () => {
    setShowHistory(!showHistory);
  };

  // If there's an active consultation, don't show the form
  if (pendingRequests.length > 0 || approvedRequests.length > 0) {
    return (
      <div className="consultation-request-container">
        <h2>Consultation Requests</h2>

        {pendingRequests.length > 0 && (
          <div className="active-requests">
            <h3>Pending Requests</h3>
            {pendingRequests.map((request, index) => (
              <div key={index} className="request-card">
                <div className="request-header">
                  <span className={getStatusBadgeClass(request.status)}>
                    {request.status.toUpperCase()}
                  </span>
                  <span className="request-id">{request.requestId}</span>
                </div>
                <div className="request-details">
                  <p><strong>Reason:</strong> {request.reason}</p>
                  <p><strong>Preferred Date:</strong> {formatDate(request.preferredDate)}</p>
                  <p><strong>Preferred Time:</strong> {formatTime(request.preferredTime)}</p>
                  <p><strong>Urgency:</strong> {request.urgency}</p>
                  <div className="request-symptoms">
                    <strong>Symptoms:</strong>
                    <p>{request.symptoms}</p>
                  </div>
                  {request.additionalNotes && (
                    <div className="request-notes">
                      <strong>Additional Notes:</strong>
                      <p>{request.additionalNotes}</p>
                    </div>
                  )}
                </div>
                <div className="request-footer">
                  <p>Requested on {formatDate(request.requestDate)}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {approvedRequests.length > 0 && (
          <div className="active-requests">
            <h3>Approved Requests</h3>
            {approvedRequests.map((request, index) => (
              <div key={index} className="request-card">
                <div className="request-header">
                  <span className={getStatusBadgeClass(request.status)}>
                    {request.status.toUpperCase()}
                  </span>
                  <span className="request-id">{request.requestId}</span>
                </div>
                <div className="request-details">
                  <p><strong>Reason:</strong> {request.reason}</p>
                  <p><strong>Appointment Date:</strong> {formatDate(request.appointmentDate || request.preferredDate)}</p>
                  <p><strong>Appointment Time:</strong> {formatTime(request.appointmentTime || request.preferredTime)}</p>
                  <div className="request-symptoms">
                    <strong>Symptoms:</strong>
                    <p>{request.symptoms}</p>
                  </div>
                </div>
                <div className="request-footer">
                  <p>Approved on {formatDate(request.approvedDate)}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {completedRequests.length > 0 && (
          <div className="request-history">
            <button
              className="toggle-history-button"
              onClick={toggleHistory}
            >
              {showHistory ? 'Hide Consultation History' : 'Show Consultation History'}
            </button>

            {showHistory && (
              <div className="history-list">
                <h3>Completed Consultations</h3>
                {completedRequests.map((request, index) => (
                  <div key={index} className="request-card history-card">
                    <div className="request-header">
                      <span className={getStatusBadgeClass(request.status)}>
                        {request.status.toUpperCase()}
                      </span>
                      <span className="request-id">{request.requestId}</span>
                    </div>
                    <div className="request-details">
                      <p><strong>Reason:</strong> {request.reason}</p>
                      <p><strong>Date:</strong> {formatDate(request.completedDate)}</p>
                      <div className="request-diagnosis">
                        <strong>Diagnosis:</strong>
                        <p>{request.diagnosis || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="new-request-section">
          <button
            className="new-request-button"
            onClick={() => {
              // In a real app, you would check if the user can create a new request
              if (pendingRequests.length === 0) {
                // Reset the form data
                setConsultationData({
                  patientId: user?.id || '',
                  patientName: user?.name || '',
                  reason: '',
                  preferredDate: '',
                  preferredTime: '',
                  symptoms: '',
                  urgency: 'normal',
                  additionalNotes: '',
                  status: 'pending'
                });
              } else {
                alert('You already have a pending consultation request.');
              }
            }}
          >
            Request New Consultation
          </button>
        </div>
      </div>
    );
  }

  // Show the form if there are no active consultations
  return (
    <div className="consultation-request-container">
      <h2>Request Medical Consultation</h2>
      <p className="form-description">
        Fill out this form to request a consultation with a medical officer.
        Once approved, a nurse will be assigned to take your vital signs before your appointment.
      </p>

      <form onSubmit={handleSubmit} className="consultation-form">
        <div className="form-group">
          <label htmlFor="medicalCenter">Select Medical Center*</label>
          <select
            id="medicalCenter"
            name="medicalCenter"
            value={consultationData.medicalCenter}
            onChange={handleChange}
            required
          >
            <option value="">Select Medical Center</option>
            <option value="Naval Medical Centre Oghara">Naval Medical Centre Oghara</option>
            <option value="HQ Logistics Command Sick Bay">HQ Logistics Command Sick Bay</option>
          </select>
          {errors.medicalCenter && <div className="error-message">{errors.medicalCenter}</div>}
        </div>

        {consultationData.medicalCenter && (
          <div className="form-group">
            <label htmlFor="reason">Reason for Consultation*</label>
            <input
              type="text"
              id="reason"
              name="reason"
              value={consultationData.reason}
              onChange={handleChange}
              placeholder="e.g., Annual Medical Checkup, Illness, Follow-up"
              required
            />
            {errors.reason && <div className="error-message">{errors.reason}</div>}
          </div>
        )}

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="preferredDate">Preferred Date*</label>
            <input
              type="date"
              id="preferredDate"
              name="preferredDate"
              value={consultationData.preferredDate}
              onChange={handleChange}
              min={new Date().toISOString().split('T')[0]}
              required
            />
            {errors.preferredDate && <div className="error-message">{errors.preferredDate}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="preferredTime">Preferred Time*</label>
            <select
              id="preferredTime"
              name="preferredTime"
              value={consultationData.preferredTime}
              onChange={handleChange}
              required
            >
              <option value="">Select Time</option>
              <option value="08:00">08:00 AM</option>
              <option value="09:00">09:00 AM</option>
              <option value="10:00">10:00 AM</option>
              <option value="11:00">11:00 AM</option>
              <option value="12:00">12:00 PM</option>
              <option value="13:00">01:00 PM</option>
              <option value="14:00">02:00 PM</option>
              <option value="15:00">03:00 PM</option>
            </select>
            {errors.preferredTime && <div className="error-message">{errors.preferredTime}</div>}
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="symptoms">Symptoms/Concerns*</label>
          <textarea
            id="symptoms"
            name="symptoms"
            value={consultationData.symptoms}
            onChange={handleChange}
            rows="4"
            placeholder="Describe your symptoms or concerns in detail"
            required
          ></textarea>
          {errors.symptoms && <div className="error-message">{errors.symptoms}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="urgency">Urgency Level</label>
          <select
            id="urgency"
            name="urgency"
            value={consultationData.urgency}
            onChange={handleChange}
          >
            <option value="low">Low - Routine checkup</option>
            <option value="normal">Normal - Minor concerns</option>
            <option value="high">High - Significant discomfort</option>
            <option value="urgent">Urgent - Severe symptoms</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="additionalNotes">Additional Notes</label>
          <textarea
            id="additionalNotes"
            name="additionalNotes"
            value={consultationData.additionalNotes}
            onChange={handleChange}
            rows="3"
            placeholder="Any additional information that might be helpful"
          ></textarea>
        </div>

        <div className="form-actions">
          <button type="submit" className="submit-button" disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </button>
        </div>
      </form>

      {completedRequests.length > 0 && (
        <div className="request-history">
          <button
            className="toggle-history-button"
            onClick={toggleHistory}
          >
            {showHistory ? 'Hide Consultation History' : 'Show Consultation History'}
          </button>

          {showHistory && (
            <div className="history-list">
              <h3>Consultation History</h3>
              {completedRequests.map((request, index) => (
                <div key={index} className="request-card history-card">
                  <div className="request-header">
                    <span className={getStatusBadgeClass(request.status)}>
                      {request.status.toUpperCase()}
                    </span>
                    <span className="request-id">{request.requestId}</span>
                  </div>
                  <div className="request-details">
                    <p><strong>Reason:</strong> {request.reason}</p>
                    <p><strong>Date:</strong> {formatDate(request.completedDate)}</p>
                    <div className="request-diagnosis">
                      <strong>Diagnosis:</strong>
                      <p>{request.diagnosis || 'Not provided'}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ConsultationRequest;
