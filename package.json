{"name": "military-biodata-project", "version": "1.0.0", "private": true, "workspaces": ["military-biodata-app-react"], "dependencies": {"axios": "^1.6.2", "bootstrap": "^5.3.3", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1"}, "scripts": {"start": "cd military-biodata-app-react && npm start", "install-all": "npm install && cd military-biodata-app-react && npm install", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}