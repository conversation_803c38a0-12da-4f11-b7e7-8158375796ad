.signup-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.signup-header {
  text-align: center;
  margin-bottom: 2rem;
}

.signup-header h1 {
  color: #003366;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.signup-header h2 {
  color: #555;
  font-size: 1.5rem;
  font-weight: 500;
}

.back-to-login-btn {
  background: none;
  border: none;
  color: #003366;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  transition: color 0.2s;
  display: inline-block;
}

.back-to-login-btn:hover {
  color: #002244;
  text-decoration: underline;
}

.progress-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}

.progress-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

.progress-step.active {
  background-color: #003366;
  color: white;
}

.progress-line {
  flex: 1;
  height: 4px;
  background-color: #e0e0e0;
  max-width: 100px;
}

.progress-line.active {
  background-color: #003366;
}

.signup-form {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-step {
  margin-bottom: 2rem;
}

.form-step h2 {
  color: #003366;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #444;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #003366;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.1);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-col {
  flex: 1;
}

.error-message {
  color: #e53935;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.prev-button,
.next-button,
.submit-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.prev-button {
  background-color: #f0f0f0;
  color: #555;
}

.prev-button:hover {
  background-color: #e0e0e0;
}

.next-button,
.submit-button {
  background-color: #003366;
  color: white;
}

.next-button:hover,
.submit-button:hover {
  background-color: #002244;
}

.next-button:disabled,
.submit-button:disabled,
.prev-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Family Information Styles */
.spouse-section,
.children-section {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.spouse-section h3,
.children-section h3 {
  color: #003366;
  margin-bottom: 1rem;
}

.child-card {
  background-color: white;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.child-card h4 {
  color: #003366;
  margin-bottom: 1rem;
}

.remove-child-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.add-child-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-child-btn:hover {
  background-color: #43a047;
}

/* File Upload Styles */
.file-upload {
  margin-bottom: 1.5rem;
}

.file-upload label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #444;
}

.file-input-container {
  position: relative;
  display: inline-block;
}

.file-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-button {
  background-color: #f0f0f0;
  color: #555;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
}

.file-name {
  margin-left: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.file-preview {
  margin-top: 1rem;
  max-width: 200px;
  max-height: 200px;
}

/* Review Step Styles */
.review-section {
  margin-bottom: 2rem;
}

.review-section h3 {
  color: #003366;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.review-item {
  display: flex;
  margin-bottom: 0.75rem;
}

.review-label {
  font-weight: 600;
  width: 200px;
  color: #555;
}

.review-value {
  flex: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .progress-bar {
    flex-wrap: wrap;
  }

  .progress-line {
    max-width: 50px;
  }
}
