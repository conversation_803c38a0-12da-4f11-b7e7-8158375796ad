.medication-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.medication-header h2 {
  margin: 0;
  color: #003366;
  font-size: 1.5rem;
}

.new-medication-btn {
  background-color: #003366;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.new-medication-btn:hover {
  background-color: #002244;
}

.medication-form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eee;
}

.medication-form-container h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #003366;
  font-size: 1.2rem;
}

.medication-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #555;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #003366;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.1);
}

.submit-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  grid-column: 1 / -1;
  margin-top: 1rem;
}

.submit-btn:hover {
  background-color: #218838;
}

.medication-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: #003366;
}

.tab-btn.active {
  color: #003366;
  border-bottom-color: #003366;
}

.medication-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.medication-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.medication-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.medication-card-header {
  background-color: #003366;
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.medication-name {
  font-weight: 600;
  font-size: 1rem;
}

.medication-status {
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
}

.medication-status.active {
  background-color: #28a745;
  color: white;
}

.medication-status.completed {
  background-color: #6c757d;
  color: white;
}

.medication-card-body {
  padding: 1rem;
}

.medication-detail {
  margin-bottom: 0.5rem;
  display: flex;
}

.detail-label {
  font-weight: 600;
  color: #555;
  width: 100px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
}

.medication-card-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
}

.refill-btn,
.mark-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  border: none;
}

.refill-btn {
  background-color: #f8f9fa;
  color: #003366;
  border: 1px solid #003366;
}

.refill-btn:hover {
  background-color: #e9ecef;
}

.mark-btn {
  background-color: #f8f9fa;
  color: #28a745;
  border: 1px solid #28a745;
}

.mark-btn:hover {
  background-color: #e9ecef;
}

.no-medications {
  text-align: center;
  color: #666;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .medication-form {
    grid-template-columns: 1fr;
  }
  
  .medication-cards {
    grid-template-columns: 1fr;
  }
  
  .medication-tabs {
    flex-direction: column;
    border-bottom: none;
  }
  
  .tab-btn {
    border-bottom: none;
    border-left: 3px solid transparent;
    text-align: left;
  }
  
  .tab-btn.active {
    border-bottom-color: transparent;
    border-left-color: #003366;
    background-color: #f0f0f0;
  }
}
