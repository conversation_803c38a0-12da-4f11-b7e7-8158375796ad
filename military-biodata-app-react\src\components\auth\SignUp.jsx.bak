
import React, { useState } from 'react';
import './signup.css';
import SignatureCanvas from '../SignatureCanvas';
import MedBlog from '../MedBlog.jsx';

const SignUp = ({ onBackToLogin }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    title: '',
    surname: '',
    firstName: '',
    middleName: '',
    serviceNumber: '',
    stateOfOrigin: '',
    dateOfBirth: '',
    sex: '',
    maritalStatus: '',
    age: '',
    bloodGroup: '',
    nin: '',
    phoneNumber: '',
    residentialAddress: '',

    // Service Information
    service: '',
    personnelCategory: '', // Changed from rankType
    rank: '',

    // Medical History
    medicalCondition: '',
    otherMedicalCondition: '',

    // Spouse Information (if married)
    spouseTitle: '',
    spouseSurname: '',
    spouseFirstName: '',
    spouseMiddleName: '',
    spouseSex: '',
    spouseIsPersonnel: '',
    spouseBranch: '',
    spousePersonnelCategory: '', // Changed from spouseRankType
    spouseRank: '',
    spouseBloodGroup: '',
    spouseDateOfBirth: '',
    spouseStateOfOrigin: '',
    spouseStateOfResidence: '',
    spouseLGA: '',

    // Children Information
    children: [],

    // Account Information
    email: '',
    password: '',
    confirmPassword: '',
    termsAgreement: false,
    confirmInformation: false,

    // Files
    passportPhoto: null,
    signature: null
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Nigerian states
  const nigerianStates = [
    "Abia", "Adamawa", "Akwa Ibom", "Anambra", "Bauchi", "Bayelsa", "Benue", "Borno",
    "Cross River", "Delta", "Ebonyi", "Edo", "Ekiti", "Enugu", "FCT", "Gombe", "Imo",
    "Jigawa", "Kaduna", "Kano", "Katsina", "Kebbi", "Kogi", "Kwara", "Lagos", "Nasarawa",
    "Niger", "Ogun", "Ondo", "Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba",
    "Yobe", "Zamfara"
  ];

  // Military ranks - focusing on Naval ranks
  const militaryRanks = {
    Navy: {
      Commissioned: ["Admiral", "Vice Admiral", "Rear Admiral", "Commodore", "Captain", "Commander", "Lieutenant Commander", "Lieutenant", "Sub-Lieutenant", "Midshipman"],
      NonCommissioned: ["Warrant Chief Petty Officer", "Chief Petty Officer", "Petty Officer", "Leading Seaman", "Able Seaman", "Ordinary Seaman", "Trainee"]
    },
    Army: {
      Commissioned: ["General", "Lieutenant General", "Major General", "Brigadier General", "Colonel", "Lieutenant Colonel", "Major", "Captain", "Lieutenant", "Second Lieutenant"],
      NonCommissioned: ["Warrant Officer", "Staff Sergeant", "Sergeant", "Corporal", "Lance Corporal", "Private"]
    },
    AirForce: {
      Commissioned: ["Air Chief Marshal", "Air Marshal", "Air Vice Marshal", "Air Commodore", "Group Captain", "Wing Commander", "Squadron Leader", "Flight Lieutenant", "Flying Officer", "Pilot Officer"],
      NonCommissioned: ["Warrant Officer", "Flight Sergeant", "Sergeant", "Corporal", "Lance Corporal", "Aircraftman"]
    }
  };

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Handle file uploads
  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setFormData({
        ...formData,
        [name]: files[0]
      });
    }
  };

  // Handle signature save
  const handleSignatureSave = (signatureData) => {
    setFormData({
      ...formData,
      signature: signatureData
    });
  };

  // Add a child
  const addChild = () => {
    if (formData.children.length < 4) {
      setFormData({
        ...formData,
        children: [
          ...formData.children,
          {
            firstName: '',
            middleName: '',
            surname: '',
            sex: '',
            bloodGroup: '',
            dateOfBirth: '',
            stateOfResidence: '',
            lga: ''
          }
        ]
      });
    }
  };

  // Update child information
  const updateChild = (index, field, value) => {
    const updatedChildren = [...formData.children];
    updatedChildren[index] = {
      ...updatedChildren[index],
      [field]: value
    };

    setFormData({
      ...formData,
      children: updatedChildren
    });
  };

  // Remove a child
  const removeChild = (index) => {
    const updatedChildren = formData.children.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      children: updatedChildren
    });
  };

  // Validate current step
  const validateStep = () => {
    const newErrors = {};

    if (currentStep === 1) {
      // Validate personal information
      if (!formData.surname) newErrors.surname = 'Surname is required';
      if (!formData.firstName) newErrors.firstName = 'First name is required';
      if (!formData.serviceNumber) newErrors.serviceNumber = 'Service number is required';
      if (!formData.stateOfOrigin) newErrors.stateOfOrigin = 'State of origin is required';
      if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
      if (!formData.sex) newErrors.sex = 'Sex is required';
      if (!formData.maritalStatus) newErrors.maritalStatus = 'Marital status is required';
      if (!formData.bloodGroup) newErrors.bloodGroup = 'Blood group is required';
      if (!formData.phoneNumber) newErrors.phoneNumber = 'Phone number is required';
      if (formData.phoneNumber && !/^\d{10,11}$/.test(formData.phoneNumber)) {
        newErrors.phoneNumber = 'Phone number must be 10-11 digits';
      }
    } else if (currentStep === 2) {
      // Validate service information
      if (!formData.service) newErrors.service = 'Your Service is required';
      if (!formData.personnelCategory) newErrors.personnelCategory = 'Personnel category is required';
      if (!formData.rank) newErrors.rank = 'Rank is required';
    } else if (currentStep === 3) {
      // Validate spouse information if married
      if (formData.maritalStatus === 'Married') {
        if (!formData.spouseSurname) newErrors.spouseSurname = 'Spouse surname is required';
        if (!formData.spouseFirstName) newErrors.spouseFirstName = 'Spouse first name is required';
        if (!formData.spouseSex) newErrors.spouseSex = 'Spouse sex is required';
        if (!formData.spouseBloodGroup) newErrors.spouseBloodGroup = 'Spouse blood group is required';
      }
    } else if (currentStep === 4) {
      // Validate account information
      if (!formData.email) newErrors.email = 'Email is required';
      if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid';
      }
      if (!formData.password) newErrors.password = 'Password is required';
      if (formData.password && formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }
      if (!formData.confirmPassword) newErrors.confirmPassword = 'Please confirm your password';
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
      if (!formData.signature) {
        newErrors.signature = 'You must draw your signature';
      }
      if (!formData.termsAgreement) {
        newErrors.termsAgreement = 'You must agree to the terms and conditions';
      }
    } else if (currentStep === 5) {
      // Validate review step
      if (!formData.confirmInformation) {
        newErrors.confirmInformation = 'You must confirm that your information is accurate';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Move to next step
  const nextStep = () => {
    if (validateStep()) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  // Move to previous step
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo(0, 0);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateStep()) {
      setIsLoading(true);

      try {
        // In a real app, you would send the form data to your backend
        // For demo purposes, we'll simulate a successful registration and store personnel data
        setTimeout(() => {
          // Create a new personnel object
          const personnelId = Date.now(); // Generate a unique ID
          const newUser = {
            id: personnelId,
            email: formData.email,
            name: `${formData.title} ${formData.firstName} ${formData.surname}`,
            rank: formData.rank,
            serviceNumber: formData.serviceNumber,
            service: formData.service,
            command: "Logistics Command",
            role: formData.personnelCategory.includes("Commissioned Officer") ? "officer" : "enlisted",
            avatar: "/images/logos/NN.png"
          };

          // Store personal information
          const personalInfo = {
            name: `${formData.title} ${formData.firstName} ${formData.surname}`,
            rank: formData.rank,
            serviceNumber: formData.serviceNumber,
            service: formData.service,
            personnelCategory: formData.personnelCategory,
            isCommissionedOfficer: formData.personnelCategory.includes("Commissioned Officer"),
            command: "Logistics Command",
            dateOfBirth: formData.dateOfBirth,
            age: calculateAge(formData.dateOfBirth),
            sex: formData.sex,
            maritalStatus: formData.maritalStatus,
            stateOfOrigin: formData.stateOfOrigin,
            lga: formData.lga,
            bloodType: formData.bloodGroup,
            genotype: formData.genotype,
            height: formData.height,
            weight: formData.weight,
            nin: formData.nin,
            phoneNumber: formData.phoneNumber,
            residentialAddress: formData.residentialAddress,
            nextOfKin: formData.nextOfKin,
            nextOfKinRelationship: formData.nextOfKinRelationship,
            nextOfKinContact: formData.nextOfKinContact,
            medicalCondition: formData.medicalCondition,
            otherMedicalCondition: formData.otherMedicalCondition
          };

          // Store spouse information if married
          if (formData.maritalStatus === "Married") {
            const spouseInfo = {
              name: `${formData.spouseTitle} ${formData.spouseFirstName} ${formData.spouseSurname}`,
              title: formData.spouseTitle,
              occupation: formData.spouseOccupation,
              phoneNumber: formData.spousePhoneNumber,
              address: formData.spouseAddress,
              service: formData.spouseService,
              rank: formData.spouseRank,
              serviceNumber: formData.spouseServiceNumber
            };
            localStorage.setItem(`spouseInfo_${personnelId}`, JSON.stringify(spouseInfo));
          }

          // Store children information if any
          if (formData.hasChildren === "Yes" && formData.children && formData.children.length > 0) {
            const childrenInfo = formData.children.map((child, index) => ({
              id: index + 1,
              name: `${child.firstName} ${child.surname}`,
              dateOfBirth: child.dateOfBirth,
              age: calculateAge(child.dateOfBirth),
              gender: child.gender,
              school: child.school
            }));
            localStorage.setItem(`childrenInfo_${personnelId}`, JSON.stringify(childrenInfo));
          }

          // Store signature if provided
          if (formData.signature) {
            localStorage.setItem(`signature_${personnelId}`, formData.signature);
          }

          // Store personal info
          localStorage.setItem(`personalInfo_${personnelId}`, JSON.stringify(personalInfo));

          // In a real app, you would store this in a database
          // For demo purposes, we'll store it in localStorage
          const existingUsers = JSON.parse(localStorage.getItem('personnel') || '[]');
          existingUsers.push(newUser);
          localStorage.setItem('personnel', JSON.stringify(existingUsers));

          setIsLoading(false);
          alert('Registration successful! You can now log in with your email and any password.');
          onBackToLogin();
        }, 2000);
      } catch (error) {
        setIsLoading(false);
        alert('Registration failed. Please try again.');
      }
    }
  };

  // Helper function to calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return '';

    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }

    return age.toString();
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderPersonalInformation();
      case 2:
        return renderServiceInformation();
      case 3:
        return renderFamilyInformation();
      case 4:
        return renderAccountInformation();
      case 5:
        return renderReview();
      default:
        return renderPersonalInformation();
    }
  };

  // Render personal information step
  const renderPersonalInformation = () => {
    return (
      <div className="form-step">
        <h2>Personal Information</h2>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="title">Title</label>
              <select
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
              >
                <option value="">Select Title</option>
                <option value="Adm">Admiral</option>
                <option value="VAdm">Vice Admiral</option>
                <option value="RAdm">Rear Admiral</option>
                <option value="Cdre">Commodore</option>
                <option value="Capt">Captain</option>
                <option value="Cdr">Commander</option>
                <option value="LtCdr">Lieutenant Commander</option>
                <option value="Lt">Lieutenant</option>
                <option value="SLt">Sub-Lieutenant</option>
                <option value="Mid">Midshipman</option>
                <option value="WCPO">Warrant Chief Petty Officer</option>
                <option value="CPO">Chief Petty Officer</option>
                <option value="PO">Petty Officer</option>
                <option value="LS">Leading Seaman</option>
                <option value="AB">Able Seaman</option>
                <option value="OS">Ordinary Seaman</option>
              </select>
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="surname">Surname*</label>
              <input
                type="text"
                id="surname"
                name="surname"
                value={formData.surname}
                onChange={handleChange}
              />
              {errors.surname && <div className="error-message">{errors.surname}</div>}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="firstName">First Name*</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
              />
              {errors.firstName && <div className="error-message">{errors.firstName}</div>}
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="middleName">Middle Name</label>
              <input
                type="text"
                id="middleName"
                name="middleName"
                value={formData.middleName}
                onChange={handleChange}
              />
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="serviceNumber">Service Number*</label>
              <input
                type="text"
                id="serviceNumber"
                name="serviceNumber"
                value={formData.serviceNumber}
                onChange={handleChange}
              />
              {errors.serviceNumber && <div className="error-message">{errors.serviceNumber}</div>}
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="stateOfOrigin">State of Origin*</label>
              <select
                id="stateOfOrigin"
                name="stateOfOrigin"
                value={formData.stateOfOrigin}
                onChange={handleChange}
              >
                <option value="">Select State</option>
                {nigerianStates.map((state) => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
              {errors.stateOfOrigin && <div className="error-message">{errors.stateOfOrigin}</div>}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="dateOfBirth">Date of Birth*</label>
              <input
                type="date"
                id="dateOfBirth"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleChange}
              />
              {errors.dateOfBirth && <div className="error-message">{errors.dateOfBirth}</div>}
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="sex">Sex*</label>
              <select
                id="sex"
                name="sex"
                value={formData.sex}
                onChange={handleChange}
              >
                <option value="">Select Sex</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
              </select>
              {errors.sex && <div className="error-message">{errors.sex}</div>}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="maritalStatus">Marital Status*</label>
              <select
                id="maritalStatus"
                name="maritalStatus"
                value={formData.maritalStatus}
                onChange={handleChange}
              >
                <option value="">Select Marital Status</option>
                <option value="Single">Single</option>
                <option value="Married">Married</option>
                <option value="Divorced">Divorced</option>
                <option value="Widowed">Widowed</option>
              </select>
              {errors.maritalStatus && <div className="error-message">{errors.maritalStatus}</div>}
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="bloodGroup">Blood Group*</label>
              <select
                id="bloodGroup"
                name="bloodGroup"
                value={formData.bloodGroup}
                onChange={handleChange}
              >
                <option value="">Select Blood Group</option>
                <option value="A+">A+</option>
                <option value="A-">A-</option>
                <option value="B+">B+</option>
                <option value="B-">B-</option>
                <option value="AB+">AB+</option>
                <option value="AB-">AB-</option>
                <option value="O+">O+</option>
                <option value="O-">O-</option>
              </select>
              {errors.bloodGroup && <div className="error-message">{errors.bloodGroup}</div>}
            </div>
          </div>
        </div>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="nin">National ID (NIN)</label>
              <input
                type="text"
                id="nin"
                name="nin"
                value={formData.nin}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number*</label>
              <input
                type="text"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                placeholder="e.g., 08012345678"
              />
              {errors.phoneNumber && <div className="error-message">{errors.phoneNumber}</div>}
            </div>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="residentialAddress">Residential Address</label>
          <textarea
            id="residentialAddress"
            name="residentialAddress"
            value={formData.residentialAddress}
            onChange={handleChange}
            rows="3"
          ></textarea>
        </div>

        <div className="form-group">
          <label htmlFor="medicalCondition">Do you have any pre-existing medical conditions?</label>
          <select
            id="medicalCondition"
            name="medicalCondition"
            value={formData.medicalCondition}
            onChange={handleChange}
          >
            <option value="">Select</option>
            <option value="None">None</option>
            <option value="Hypertension">Hypertension</option>
            <option value="Diabetes">Diabetes</option>
            <option value="Asthma">Asthma</option>
            <option value="Allergies">Allergies</option>
            <option value="Other">Other (please specify)</option>
          </select>
        </div>

        {formData.medicalCondition === 'Other' && (
          <div className="form-group">
            <label htmlFor="otherMedicalCondition">Please specify your medical condition</label>
            <textarea
              id="otherMedicalCondition"
              name="otherMedicalCondition"
              value={formData.otherMedicalCondition}
              onChange={handleChange}
              rows="3"
            ></textarea>
          </div>
        )}
      </div>
    );
  };

  // Render service information step
  const renderServiceInformation = () => {
    return (
      <div className="form-step">
        <h2>Service Information</h2>

        <div className="form-row">
          <div className="form-col">
            <div className="form-group">
              <label htmlFor="service">Military Service*</label>
              <select
                id="service"
                name="service"
                value={formData.service}
                onChange={handleChange}
              >
                <option value="Navy">Navy</option>
                <option value="Army">Army</option>
                <option value="AirForce">Air Force</option>
              </select>
              {errors.service && <div className="error-message">{errors.service}</div>}
            </div>
          </div>

          <div className="form-col">
            <div className="form-group">
              <label htmlFor="personnelCategory">Personnel Category*</label>
              <select
                id="personnelCategory"
                name="personnelCategory"
                value={formData.personnelCategory}
                onChange={handleChange}
              >
                <option value="">Select Personnel Category</option>
                <option value="Commissioned">Commissioned Officer</option>
                <option value="NonCommissioned">Non-Commissioned Officer</option>
              </select>
              {errors.personnelCategory && <div className="error-message">{errors.personnelCategory}</div>}
            </div>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="rank">Rank*</label>
          <select
            id="rank"
            name="rank"
            value={formData.rank}
            onChange={handleChange}
          >
            <option value="">Select Rank</option>
            {formData.service && formData.personnelCategory &&
              militaryRanks[formData.service][formData.personnelCategory].map((rank) => (
                <option key={rank} value={rank}>{rank}</option>
              ))
            }
          </select>
          {errors.rank && <div className="error-message">{errors.rank}</div>}
        </div>



        <div className="file-upload">
          <label>Upload Passport Photograph</label>
          <div className="file-input-container">
            <button type="button" className="file-input-button">Choose File</button>
            <input
              type="file"
              name="passportPhoto"
              className="file-input"
              accept="image/*"
              onChange={handleFileChange}
            />
            <span className="file-name">
              {formData.passportPhoto ? formData.passportPhoto.name : 'No file chosen'}
            </span>
          </div>
          {formData.passportPhoto && (
            <img
              src={URL.createObjectURL(formData.passportPhoto)}
              alt="Passport Preview"
              className="file-preview"
            />
          )}
        </div>

        <div className="file-upload">
          <label>Upload Signature</label>
          <div className="file-input-container">
            <button type="button" className="file-input-button">Choose File</button>
            <input
              type="file"
              name="signature"
              className="file-input"
              accept="image/*"
              onChange={handleFileChange}
            />
            <span className="file-name">
              {formData.signature ? formData.signature.name : 'No file chosen'}
            </span>
          </div>
          {formData.signature && (
            <img
              src={URL.createObjectURL(formData.signature)}
              alt="Signature Preview"
              className="file-preview"
            />
          )}
        </div>
      </div>
    );
  };

  // Render family information step
  const renderFamilyInformation = () => {
    return (
      <div className="form-step">
        <h2>Family Information</h2>

        {formData.maritalStatus === 'Married' && (
          <div className="spouse-section">
            <h3>Spouse Information</h3>

            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseTitle">Title</label>
                  <select
                    id="spouseTitle"
                    name="spouseTitle"
                    value={formData.spouseTitle}
                    onChange={handleChange}
                  >
                    <option value="">Select Title</option>
                    <option value="Mr">Mr</option>
                    <option value="Mrs">Mrs</option>
                    <option value="Miss">Miss</option>
                    <option value="Dr">Dr</option>
                    <option value="Adm">Admiral</option>
                    <option value="VAdm">Vice Admiral</option>
                    <option value="RAdm">Rear Admiral</option>
                    <option value="Cdre">Commodore</option>
                    <option value="Capt">Captain</option>
                    <option value="Cdr">Commander</option>
                    <option value="LtCdr">Lieutenant Commander</option>
                    <option value="Lt">Lieutenant</option>
                    <option value="SLt">Sub-Lieutenant</option>
                    <option value="Mid">Midshipman</option>
                    <option value="WCPO">Warrant Chief Petty Officer</option>
                    <option value="CPO">Chief Petty Officer</option>
                    <option value="PO">Petty Officer</option>
                    <option value="LS">Leading Seaman</option>
                    <option value="AB">Able Seaman</option>
                    <option value="OS">Ordinary Seaman</option>
                  </select>
                </div>
              </div>

              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseSurname">Surname*</label>
                  <input
                    type="text"
                    id="spouseSurname"
                    name="spouseSurname"
                    value={formData.spouseSurname}
                    onChange={handleChange}
                  />
                  {errors.spouseSurname && <div className="error-message">{errors.spouseSurname}</div>}
                </div>
              </div>
            </div>

            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseFirstName">First Name*</label>
                  <input
                    type="text"
                    id="spouseFirstName"
                    name="spouseFirstName"
                    value={formData.spouseFirstName}
                    onChange={handleChange}
                  />
                  {errors.spouseFirstName && <div className="error-message">{errors.spouseFirstName}</div>}
                </div>
              </div>

              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseMiddleName">Middle Name</label>
                  <input
                    type="text"
                    id="spouseMiddleName"
                    name="spouseMiddleName"
                    value={formData.spouseMiddleName}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseSex">Sex*</label>
                  <select
                    id="spouseSex"
                    name="spouseSex"
                    value={formData.spouseSex}
                    onChange={handleChange}
                  >
                    <option value="">Select Sex</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                  </select>
                  {errors.spouseSex && <div className="error-message">{errors.spouseSex}</div>}
                </div>
              </div>

              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseBloodGroup">Blood Group*</label>
                  <select
                    id="spouseBloodGroup"
                    name="spouseBloodGroup"
                    value={formData.spouseBloodGroup}
                    onChange={handleChange}
                  >
                    <option value="">Select Blood Group</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                  {errors.spouseBloodGroup && <div className="error-message">{errors.spouseBloodGroup}</div>}
                </div>
              </div>
            </div>

            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseDateOfBirth">Date of Birth</label>
                  <input
                    type="date"
                    id="spouseDateOfBirth"
                    name="spouseDateOfBirth"
                    value={formData.spouseDateOfBirth}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="form-col">
                <div className="form-group">
                  <label htmlFor="spouseStateOfOrigin">State of Origin</label>
                  <select
                    id="spouseStateOfOrigin"
                    name="spouseStateOfOrigin"
                    value={formData.spouseStateOfOrigin}
                    onChange={handleChange}
                  >
                    <option value="">Select State</option>
                    {nigerianStates.map((state) => (
                      <option key={state} value={state}>{state}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="spouseIsPersonnel">Is your spouse a military personnel?</label>
              <select
                id="spouseIsPersonnel"
                name="spouseIsPersonnel"
                value={formData.spouseIsPersonnel}
                onChange={handleChange}
              >
                <option value="">Select</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
            </div>

            {formData.spouseIsPersonnel === 'Yes' && (
              <>
                <div className="form-row">
                  <div className="form-col">
                    <div className="form-group">
                      <label htmlFor="spouseService">Military Service</label>
                      <select
                        id="spouseService"
                        name="spouseService"
                        value={formData.spouseService}
                        onChange={handleChange}
                      >
                        <option value="Navy">Navy</option>
                        <option value="Army">Army</option>
                        <option value="AirForce">Air Force</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-col">
                    <div className="form-group">
                      <label htmlFor="spousePersonnelCategory">Personnel Category</label>
                      <select
                        id="spousePersonnelCategory"
                        name="spousePersonnelCategory"
                        value={formData.spousePersonnelCategory}
                        onChange={handleChange}
                        disabled={!formData.spouseService}
                      >
                        <option value="">Select Personnel Category</option>
                        <option value="Commissioned">Commissioned Officer</option>
                        <option value="NonCommissioned">Non-Commissioned Officer</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="spouseRank">Rank</label>
                  <select
                    id="spouseRank"
                    name="spouseRank"
                    value={formData.spouseRank}
                    onChange={handleChange}
                    disabled={!formData.spouseService || !formData.spousePersonnelCategory}
                  >
                    <option value="">Select Rank</option>
                    {formData.spouseService && formData.spousePersonnelCategory &&
                      militaryRanks[formData.spouseService][formData.spousePersonnelCategory].map((rank) => (
                        <option key={rank} value={rank}>{rank}</option>
                      ))
                    }
                  </select>
                </div>
              </>
            )}
          </div>
        )}

        <div className="children-section">
          <h3>Children Information</h3>
          <p>You can add up to 4 children</p>

          {formData.children.map((child, index) => (
            <div key={index} className="child-card">
              <h4>Child {index + 1}</h4>
              <button
                type="button"
                className="remove-child-btn"
                onClick={() => removeChild(index)}
              >
                ×
              </button>

              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label htmlFor={`child-surname-${index}`}>Surname</label>
                    <input
                      type="text"
                      id={`child-surname-${index}`}
                      value={child.surname}
                      onChange={(e) => updateChild(index, 'surname', e.target.value)}
                    />
                  </div>
                </div>

                <div className="form-col">
                  <div className="form-group">
                    <label htmlFor={`child-firstName-${index}`}>First Name</label>
                    <input
                      type="text"
                      id={`child-firstName-${index}`}
                      value={child.firstName}
                      onChange={(e) => updateChild(index, 'firstName', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label htmlFor={`child-sex-${index}`}>Sex</label>
                    <select
                      id={`child-sex-${index}`}
                      value={child.sex}
                      onChange={(e) => updateChild(index, 'sex', e.target.value)}
                    >
                      <option value="">Select Sex</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                </div>

                <div className="form-col">
                  <div className="form-group">
                    <label htmlFor={`child-dateOfBirth-${index}`}>Date of Birth</label>
                    <input
                      type="date"
                      id={`child-dateOfBirth-${index}`}
                      value={child.dateOfBirth}
                      onChange={(e) => updateChild(index, 'dateOfBirth', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label htmlFor={`child-bloodGroup-${index}`}>Blood Group</label>
                    <select
                      id={`child-bloodGroup-${index}`}
                      value={child.bloodGroup}
                      onChange={(e) => updateChild(index, 'bloodGroup', e.target.value)}
                    >
                      <option value="">Select Blood Group</option>
                      <option value="A+">A+</option>
                      <option value="A-">A-</option>
                      <option value="B+">B+</option>
                      <option value="B-">B-</option>
                      <option value="AB+">AB+</option>
                      <option value="AB-">AB-</option>
                      <option value="O+">O+</option>
                      <option value="O-">O-</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {formData.children.length < 4 && (
            <button
              type="button"
              className="add-child-btn"
              onClick={addChild}
            >
              + Add Child
            </button>
          )}
        </div>
      </div>
    );
  };

  // Render account information step
  const renderAccountInformation = () => {
    return (
      <div className="form-step">
        <h2>Account Information</h2>
        <p>Create your login credentials for the Logistics Command Medical Portal</p>

        <div className="form-group">
          <label htmlFor="email">Email Address*</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="e.g., <EMAIL>"
          />
          {errors.email && <div className="error-message">{errors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="password">Password*</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
          />
          {errors.password && <div className="error-message">{errors.password}</div>}
          <div className="password-requirements">
            <p>Password must be at least 8 characters long and include:</p>
            <ul>
              <li>At least one uppercase letter</li>
              <li>At least one lowercase letter</li>
              <li>At least one number</li>
              <li>At least one special character</li>
            </ul>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="confirmPassword">Confirm Password*</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
          />
          {errors.confirmPassword && <div className="error-message">{errors.confirmPassword}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="signature">Your Signature*</label>
          <SignatureCanvas onSave={handleSignatureSave} initialSignature={formData.signature} />
          {!formData.signature && <div className="error-message">Please draw your signature</div>}
        </div>

        <div className="form-group">
          <div className="checkbox-group">
            <input
              type="checkbox"
              id="termsAgreement"
              name="termsAgreement"
              checked={formData.termsAgreement}
              onChange={(e) => setFormData({...formData, termsAgreement: e.target.checked})}
            />
            <label htmlFor="termsAgreement">
              I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
            </label>
          </div>
          {errors.termsAgreement && <div className="error-message">{errors.termsAgreement}</div>}
        </div>
      </div>
    );
  };

  // Render review step
  const renderReview = () => {
    return (
      <div className="form-step">
        <h2>Review Your Information</h2>
        <p>Please review your information before submitting</p>

        <div className="review-section">
          <h3>Personal Information</h3>
          <div className="review-item">
            <div className="review-label">Full Name:</div>
            <div className="review-value">
              {formData.title} {formData.firstName} {formData.middleName} {formData.surname}
            </div>
          </div>
          <div className="review-item">
            <div className="review-label">Service Number:</div>
            <div className="review-value">{formData.serviceNumber}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Date of Birth:</div>
            <div className="review-value">{formData.dateOfBirth}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Sex:</div>
            <div className="review-value">{formData.sex}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Marital Status:</div>
            <div className="review-value">{formData.maritalStatus}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Blood Group:</div>
            <div className="review-value">{formData.bloodGroup}</div>
          </div>
          <div className="review-item">
            <div className="review-label">State of Origin:</div>
            <div className="review-value">{formData.stateOfOrigin}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Phone Number:</div>
            <div className="review-value">{formData.phoneNumber}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Address:</div>
            <div className="review-value">{formData.residentialAddress}</div>
          </div>
        </div>

        <div className="review-section">
          <h3>Service Information</h3>
          <div className="review-item">
            <div className="review-label">Branch:</div>
            <div className="review-value">{formData.branch}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Rank:</div>
            <div className="review-value">{formData.rank}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Medical Condition:</div>
            <div className="review-value">
              {formData.medicalCondition === 'Other'
                ? `${formData.medicalCondition} - ${formData.otherMedicalCondition}`
                : formData.medicalCondition}
            </div>
          </div>
        </div>

        {formData.maritalStatus === 'Married' && (
          <div className="review-section">
            <h3>Spouse Information</h3>
            <div className="review-item">
              <div className="review-label">Full Name:</div>
              <div className="review-value">
                {formData.spouseTitle} {formData.spouseFirstName} {formData.spouseMiddleName} {formData.spouseSurname}
              </div>
            </div>
            <div className="review-item">
              <div className="review-label">Sex:</div>
              <div className="review-value">{formData.spouseSex}</div>
            </div>
            <div className="review-item">
              <div className="review-label">Blood Group:</div>
              <div className="review-value">{formData.spouseBloodGroup}</div>
            </div>
            {formData.spouseIsPersonnel === 'Yes' && (
              <>
                <div className="review-item">
                  <div className="review-label">Military Branch:</div>
                  <div className="review-value">{formData.spouseBranch}</div>
                </div>
                <div className="review-item">
                  <div className="review-label">Rank:</div>
                  <div className="review-value">{formData.spouseRank}</div>
                </div>
              </>
            )}
          </div>
        )}

        {formData.children.length > 0 && (
          <div className="review-section">
            <h3>Children Information</h3>
            {formData.children.map((child, index) => (
              <div key={index} className="review-child">
                <h4>Child {index + 1}</h4>
                <div className="review-item">
                  <div className="review-label">Full Name:</div>
                  <div className="review-value">{child.firstName} {child.surname}</div>
                </div>
                <div className="review-item">
                  <div className="review-label">Sex:</div>
                  <div className="review-value">{child.sex}</div>
                </div>
                <div className="review-item">
                  <div className="review-label">Date of Birth:</div>
                  <div className="review-value">{child.dateOfBirth}</div>
                </div>
                <div className="review-item">
                  <div className="review-label">Blood Group:</div>
                  <div className="review-value">{child.bloodGroup}</div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="review-section">
          <h3>Account Information</h3>
          <div className="review-item">
            <div className="review-label">Email:</div>
            <div className="review-value">{formData.email}</div>
          </div>
          <div className="review-item">
            <div className="review-label">Signature:</div>
            <div className="review-value">
              {formData.signature && (
                <img
                  src={formData.signature}
                  alt="Your signature"
                  style={{ maxWidth: '200px', maxHeight: '100px', border: '1px solid #ccc' }}
                />
              )}
            </div>
          </div>
        </div>

        <div className="form-group">
          <div className="checkbox-group">
            <input
              type="checkbox"
              id="confirmInformation"
              name="confirmInformation"
              checked={formData.confirmInformation}
              onChange={(e) => setFormData({...formData, confirmInformation: e.target.checked})}
            />
            <label htmlFor="confirmInformation">
              I confirm that all the information provided is accurate and complete
            </label>
          </div>
          {errors.confirmInformation && <div className="error-message">{errors.confirmInformation}</div>}
        </div>
      </div>
    );
  };

  return (
    <div className="signup-container">
      <div className="signup-header">
        <h1>Logistics Command Medical Portal</h1>
        <h2>Personnel Registration</h2>
        <button onClick={onBackToLogin} className="back-to-login-btn">
          &larr; Back to Login
        </button>
      </div>

      <div className="progress-bar">
        <div className={`progress-step ${currentStep >= 1 ? 'active' : ''}`}>1</div>
        <div className={`progress-line ${currentStep >= 2 ? 'active' : ''}`}></div>
        <div className={`progress-step ${currentStep >= 2 ? 'active' : ''}`}>2</div>
        <div className={`progress-line ${currentStep >= 3 ? 'active' : ''}`}></div>
        <div className={`progress-step ${currentStep >= 3 ? 'active' : ''}`}>3</div>
        <div className={`progress-line ${currentStep >= 4 ? 'active' : ''}`}></div>
        <div className={`progress-step ${currentStep >= 4 ? 'active' : ''}`}>4</div>
        <div className={`progress-line ${currentStep >= 5 ? 'active' : ''}`}></div>
        <div className={`progress-step ${currentStep >= 5 ? 'active' : ''}`}>5</div>
      </div>

      <form onSubmit={handleSubmit} className="signup-form">
        {renderStep()}

        <div className="form-navigation">
          {currentStep > 1 && (
            <button
              type="button"
              className="prev-button"
              onClick={prevStep}
              disabled={isLoading}
            >
              Previous
            </button>
          )}

          {currentStep < 5 ? (
            <button
              type="button"
              className="next-button"
              onClick={nextStep}
              disabled={isLoading}
            >
              Next
            </button>
          ) : (
            <button
              type="submit"
              className="submit-button"
              disabled={isLoading}
            >
              {isLoading ? 'Submitting...' : 'Submit'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default SignUp;






