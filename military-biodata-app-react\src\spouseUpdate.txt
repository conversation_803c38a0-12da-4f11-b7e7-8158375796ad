﻿                      <label htmlFor="spousePersonnelCategory">Personnel Category</label>
                      <select
                        id="spousePersonnelCategory"
                        name="spousePersonnelCategory"
                        value={formData.spousePersonnelCategory}
                        onChange={handleChange}
                        disabled={!formData.spouseService}
                      >
                        <option value="">Select Category</option>
                        <option value="Commissioned Officer">Commissioned Officer</option>
                        {formData.spouseService === "Navy" && <option value="Rating">Rating</option>}
                        {formData.spouseService === "Army" && <option value="Soldier">Soldier</option>}
                        {formData.spouseService === "AirForce" && <option value="Airman/Airwoman">Airman/Airwoman</option>}
                      </select>
