.medical-staff-dashboard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid #003366;
  padding-bottom: 1rem;
}

.dashboard-header h2 {
  color: #003366;
  margin: 0;
}

.staff-info {
  text-align: right;
}

.staff-info p {
  margin: 0;
  font-weight: 500;
}

.staff-role {
  color: #666;
  font-size: 0.9rem;
}

.dashboard-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 2rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.tab-button:hover {
  color: #003366;
}

.tab-button.active {
  color: #003366;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #003366;
}

.dashboard-content {
  min-height: 400px;
}

.consultations-tab h3,
.patient-records-tab h3 {
  color: #003366;
  margin: 1.5rem 0 1rem;
  font-size: 1.2rem;
}

.request-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.request-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #eee;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.completed-card {
  opacity: 0.85;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background-color: #fff4e5;
  color: #ff9800;
  border: 1px solid #ff9800;
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.status-badge.completed {
  background-color: #e3f2fd;
  color: #2196f3;
  border: 1px solid #2196f3;
}

.status-badge.cancelled {
  background-color: #ffebee;
  color: #f44336;
  border: 1px solid #f44336;
}

.request-id {
  font-size: 0.9rem;
  color: #666;
}

.request-patient {
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #f0f7ff;
  border-radius: 4px;
  border-left: 3px solid #003366;
}

.request-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.request-details p {
  margin: 0;
}

.request-symptoms {
  margin-top: 0.5rem;
}

.request-symptoms p {
  margin-top: 0.25rem;
  padding: 0.75rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #003366;
}

.request-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.view-patient-btn,
.approve-btn,
.complete-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-patient-btn {
  background-color: transparent;
  color: #003366;
  border: 1px solid #003366;
}

.view-patient-btn:hover {
  background-color: #f0f7ff;
}

.approve-btn {
  background-color: #4caf50;
  color: white;
  border: none;
}

.approve-btn:hover {
  background-color: #43a047;
}

.complete-btn {
  background-color: #2196f3;
  color: white;
  border: none;
}

.complete-btn:hover {
  background-color: #1e88e5;
}

.no-data-message {
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #666;
  text-align: center;
  font-style: italic;
}

.patient-info-card {
  background-color: #f0f7ff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #cce5ff;
}

.patient-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.patient-details p {
  margin: 0;
}

.patient-records-section {
  margin-bottom: 2rem;
}

.records-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  margin-top: 1rem;
}

.records-table th,
.records-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.records-table th {
  background-color: #f0f7ff;
  color: #003366;
  font-weight: 600;
}

.records-table tr:hover {
  background-color: #f9f9f9;
}

.prescriptions-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.prescription-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #eee;
}

.prescription-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.prescription-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prescription-medications ul {
  margin-top: 0.5rem;
  padding-left: 1.5rem;
}

.prescription-medications li {
  margin-bottom: 0.5rem;
}

.select-patient-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #f5f5f5;
  border-radius: 8px;
  text-align: center;
  padding: 2rem;
}

.vital-signs-section,
.prescription-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .staff-info {
    text-align: left;
  }
  
  .patient-details {
    grid-template-columns: 1fr;
  }
  
  .request-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .records-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}
