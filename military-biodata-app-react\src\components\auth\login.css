/* Military Biodata Login Styles */
body {
  font-family: "Segoe UI", Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.login-container {
  background: linear-gradient(135deg, #1a3a5f 0%, #0d253f 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.login-nav {
  position: absolute;
  top: 1rem;
  right: 2rem;
  display: flex;
  gap: 1rem;
  z-index: 10;
}

.nav-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 800px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.login-logo:hover {
  transform: scale(1.05);
}

.login-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.login-header p {
  max-width: 600px;
  line-height: 1.6;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.login-form {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  width: 100%;
  max-width: 450px;
  color: #333;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  margin-top: 2rem;
}

.login-title {
  font-size: 1.8rem;
  color: #1a3a5f;
  margin-top: 0;
  margin-bottom: 0.5rem;
  text-align: center;
}

.login-subtitle {
  color: #666;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 0.95rem;
}

.login-error {
  color: #e53935;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
  background-color: rgba(229, 57, 53, 0.1);
  padding: 0.5rem;
  border-radius: 4px;
}

.login-form label {
  display: block;
  margin-top: 1rem;
  font-weight: 600;
  color: #1a3a5f;
  font-size: 0.9rem;
}

.login-form input {
  width: 100%;
  padding: 0.85rem 1rem;
  margin-top: 0.25rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

.login-form input:focus {
  border-color: #1a3a5f;
  outline: none;
  box-shadow: 0 0 0 3px rgba(26, 58, 95, 0.15);
  background-color: #fff;
}

.form-footer {
  margin-top: 1.5rem;
}

.password-hint {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  margin-bottom: 1rem;
  font-style: italic;
}

.login-button {
  background: linear-gradient(135deg, #1a3a5f 0%, #0d253f 100%);
  color: white;
  padding: 0.85rem;
  width: 100%;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.05rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  background: linear-gradient(135deg, #0d253f 0%, #1a3a5f 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.signup-link {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.signup-link-button {
  background: none;
  border: none;
  color: #1a3a5f;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  font-size: 0.9rem;
  text-decoration: underline;
}

.signup-link-button:hover {
  color: #0d253f;
}

.guest-view-container {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 10px;
  width: 100%;
  max-width: 1000px;
  margin-top: 2rem;
  color: #333;
}
