{"name": "military-biodata-app-react", "version": "1.0.0", "private": true, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "^6.20.0", "axios": "1.4.0", "chart.js": "4.3.3", "history": "5.3.0", "react-icons": "^4.10.1", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "audit:fix": "npm audit fix --force", "postinstall": "npm audit fix --force", "secure-install": "npm install --legacy-peer-deps && npm audit fix --force"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}