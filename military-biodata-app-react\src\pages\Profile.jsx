import React, { useState, useEffect } from 'react';
import '../styles/Profile.css';

const Profile = () => {
  const [profile] = useState({
    personal: {
      name: 'Lt. <PERSON>',
      rank: 'Lieutenant',
      serviceNo: 'NN/0123/21',
      unit: '101 Naval Division',
      command: 'Western Naval Command',
      dob: '1990-05-15',
      bloodGroup: 'O+',
      gender: 'Male',
      height: '182',
      weight: '78'
    },
    medicalHistory: [
      { date: '2023-01-15', complaint: 'Recurring headaches', diagnosis: 'Tension headache', treatment: 'Prescribed Ibuprofen 400mg', doctor: 'Dr. <PERSON>' },
      { date: '2022-11-03', complaint: 'Sprained ankle', diagnosis: 'Grade 1 ankle sprain', treatment: 'RICE protocol, ankle brace', doctor: 'Dr. <PERSON>' }
    ],
    allergies: ['Penicillin', 'Dust mites'],
    annualCheckups: [
      { date: '2023-02-10', status: 'Completed', notes: 'All tests normal', doctor: 'Dr. <PERSON>' },
      { date: '2022-02-15', status: 'Completed', notes: 'Slightly elevated cholesterol', doctor: 'Dr. <PERSON>' }
    ],
    bpRecords: [
      { date: '2023-03-01', systolic: 120, diastolic: 80, pulse: 72 },
      { date: '2023-02-01', systolic: 118, diastolic: 78, pulse: 70 },
      { date: '2023-01-01', systolic: 122, diastolic: 82, pulse: 74 }
    ],
    eyeTests: [
      { date: '2023-02-10', leftEye: '6/6', rightEye: '6/6', colorVision: 'Normal' },
      { date: '2022-02-15', leftEye: '6/6', rightEye: '6/9', colorVision: 'Normal' }
    ],
    weightHistory: [
      { date: '2023-02-10', weight: 78, bmi: 23.5, classification: 'Normal' },
      { date: '2022-02-15', weight: 80, bmi: 24.2, classification: 'Normal' }
    ]
  });
  
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // In a real app, fetch profile data from API
    setLoading(true);
    // Mock API call
    setTimeout(() => {
      setLoading(false);
    }, 500);
  }, []);

  const calculateBMI = (weight, height) => {
    const heightInMeters = height / 100;
    return (weight / (heightInMeters * heightInMeters)).toFixed(1);
  };

  const currentBMI = calculateBMI(profile.personal.weight, profile.personal.height);

  if (loading) return <div className="loading">Loading profile data...</div>;

  return (
    <div className="profile-container">
      <h1>{profile.personal.name}</h1>
      <p>BMI: {currentBMI}</p>
      {/* Add more profile details as needed */}
    </div>
  );
};

export default Profile;
