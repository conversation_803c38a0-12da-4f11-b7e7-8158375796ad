require('dotenv').config();
const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(express.json());
app.use(cors());

// Basic route for testing
app.get('/', (req, res) => {
  console.log('Root endpoint accessed');
  res.json({ message: 'Military Biodata API is running' });
});

// Mock auth routes
app.post('/api/auth/register', (req, res) => {
  console.log('Register endpoint accessed', req.body);
  res.json({ success: true, message: 'User registered successfully' });
});

app.post('/api/auth/login', (req, res) => {
  console.log('Login endpoint accessed', req.body);
  const { serviceNo, password } = req.body;
  // Mock authentication
  res.json({ 
    token: 'mock-jwt-token',
    user: {
      id: '123',
      serviceNo,
      role: 'personnel'
    }
  });
});

// Mock dashboard routes
app.get('/api/dashboard/stats', (req, res) => {
  console.log('Dashboard stats endpoint accessed');
  res.json({ 
    bp: [
      { date: '2023-01-01', systolic: 120, diastolic: 80 },
      { date: '2023-02-01', systolic: 118, diastolic: 78 }
    ],
    annuals: [
      { date: '2023-01-15', status: 'completed' }
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Server error occurred' });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('- GET  /');
  console.log('- POST /api/auth/register');
  console.log('- POST /api/auth/login');
  console.log('- GET  /api/dashboard/stats');
});

