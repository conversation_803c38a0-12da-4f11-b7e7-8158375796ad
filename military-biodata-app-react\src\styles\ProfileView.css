.profile-view-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.profile-header {
  background: linear-gradient(135deg, #003366 0%, #004080 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.profile-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.5);
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.profile-avatar-section h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.profile-avatar-section p {
  margin: 0.25rem 0;
  opacity: 0.9;
  font-size: 1rem;
}

.profile-actions {
  display: flex;
  gap: 1rem;
}

.edit-button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.edit-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.edit-button.save-mode {
  background-color: #4caf50;
  border-color: #4caf50;
}

.edit-button.save-mode:hover {
  background-color: #43a047;
}

.profile-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  background-color: #f9f9f9;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.tab-button:hover {
  color: #003366;
}

.tab-button.active {
  color: #003366;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #003366;
}

.profile-content {
  padding: 2rem;
}

.profile-tab-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.profile-section h3 {
  color: #003366;
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
  font-size: 1.2rem;
}

.profile-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.profile-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-info-item.full-width {
  grid-column: span 2;
}

.info-label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.info-value {
  font-size: 1rem;
  color: #333;
}

.edit-input {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

.edit-input:focus {
  border-color: #003366;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.2);
}

.edit-input.textarea {
  resize: vertical;
  min-height: 80px;
}

.child-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eee;
}

.child-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.child-card-header h4 {
  margin: 0;
  color: #003366;
  font-size: 1.1rem;
}

.remove-child-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.remove-child-btn:hover {
  background-color: #d32f2f;
}

.add-child-btn {
  background-color: #003366;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  margin-top: 1rem;
  display: inline-block;
}

.add-child-btn:hover {
  background-color: #004080;
}

.no-data-message {
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #666;
  text-align: center;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }
  
  .profile-actions {
    width: 100%;
    justify-content: center;
  }
  
  .profile-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .profile-info-grid {
    grid-template-columns: 1fr;
  }
  
  .profile-info-item.full-width {
    grid-column: span 1;
  }
}
