import React, { useState, useEffect } from 'react';
import '../../styles/Dashboard.css';
import AppointmentScheduler from '../AppointmentScheduler';
import MedicationTracker from '../MedicationTracker';
import ConsultationRequest from '../ConsultationRequest';

const UserDashboard = ({ user }) => {
  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    medicalHistory: false,
    consultationRequests: true,
    appointments: false,
    medications: false
  });

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // State for personal info
  const [personalInfo, setPersonalInfo] = useState({
    name: 'Loading...',
    rank: 'Loading...',
    serviceNumber: 'Loading...',
    branch: 'Loading...',
    division: 'Loading...',
    dateOfBirth: 'Loading...',
    age: 'Loading...',
    sex: 'Loading...',
    maritalStatus: 'Loading...',
    stateOfOrigin: 'Loading...',
    lga: 'Loading...',
    bloodType: 'Loading...',
    genotype: 'Loading...',
    height: 'Loading...',
    weight: 'Loading...',
    nin: 'Loading...',
    phoneNumber: 'Loading...',
    residentialAddress: 'Loading...',
    nextOfKin: 'Loading...',
    nextOfKinRelationship: 'Loading...',
    nextOfKinContact: 'Loading...'
  });

  // Update personal info when user changes
  useEffect(() => {
    if (user) {
      // Get user-specific data
      let userData = {
        name: user.name,
        rank: user.rank,
        serviceNumber: user.serviceNumber,
        branch: user.branch
      };

      // Add additional data based on user
      if (user.id === 1) { // John Doe
        userData = {
          ...userData,
          division: '3rd Infantry Division',
          dateOfBirth: '1990-05-15',
          age: '34',
          sex: 'Male',
          maritalStatus: 'Married',
          stateOfOrigin: 'Lagos',
          lga: 'Ikeja',
          bloodType: 'O+',
          genotype: 'AA',
          height: '182 cm',
          weight: '78 kg',
          nin: '12345678901',
          phoneNumber: '08012345678',
          residentialAddress: '123 Military Quarters, Ikeja Cantonment, Lagos',
          nextOfKin: 'Jane Doe',
          nextOfKinRelationship: 'Wife',
          nextOfKinContact: '08087654321'
        };
      } else if (user.id === 2) { // Sarah Wilson
        userData = {
          ...userData,
          division: '5th Air Wing',
          dateOfBirth: '1988-09-22',
          age: '36',
          sex: 'Female',
          maritalStatus: 'Single',
          stateOfOrigin: 'Abuja',
          lga: 'Municipal Area Council',
          bloodType: 'A-',
          genotype: 'AS',
          height: '170 cm',
          weight: '65 kg',
          nin: '23456789012',
          phoneNumber: '08023456789',
          residentialAddress: '456 Air Force Base, Abuja',
          nextOfKin: 'Robert Wilson',
          nextOfKinRelationship: 'Father',
          nextOfKinContact: '08076543210'
        };
      } else if (user.id === 3) { // Michael Johnson
        userData = {
          ...userData,
          division: '2nd Marine Division',
          dateOfBirth: '1992-03-10',
          age: '32',
          sex: 'Male',
          maritalStatus: 'Married',
          stateOfOrigin: 'Rivers',
          lga: 'Port Harcourt',
          bloodType: 'B+',
          genotype: 'AA',
          height: '188 cm',
          weight: '85 kg',
          nin: '34567890123',
          phoneNumber: '08034567890',
          residentialAddress: '789 Naval Base, Port Harcourt, Rivers',
          nextOfKin: 'Elizabeth Johnson',
          nextOfKinRelationship: 'Wife',
          nextOfKinContact: '08065432109'
        };
      }

      setPersonalInfo(userData);
    }
  }, [user]);

  // State for medical records
  const [medicalRecords, setMedicalRecords] = useState([
    { date: 'Loading...', type: 'Loading...', status: 'Loading...', result: 'Loading...' }
  ]);

  // State for deployment history
  const [deploymentHistory, setDeploymentHistory] = useState([
    { period: 'Loading...', location: 'Loading...', role: 'Loading...' }
  ]);

  // State for fitness scores
  const [fitnessScores, setFitnessScores] = useState([
    { date: 'Loading...', pushUps: 0, sitUps: 0, runTime: 'Loading...', score: 0 }
  ]);

  // Update medical records, deployment history, and fitness scores when user changes
  useEffect(() => {
    if (user) {
      // User-specific medical records
      if (user.id === 1) { // John Doe
        setMedicalRecords([
          { date: '2024-01-15', type: 'Annual Physical', status: 'Completed', result: 'Passed' },
          { date: '2023-08-22', type: 'Vaccination', status: 'Completed', result: 'Administered' },
          { date: '2023-05-10', type: 'Dental Checkup', status: 'Completed', result: 'Passed' },
          { date: '2023-02-05', type: 'Vision Test', status: 'Completed', result: 'Passed' }
        ]);

        setDeploymentHistory([
          { period: 'Jan 2023 - Jun 2023', location: 'Fort Benning, GA', role: 'Training Officer' },
          { period: 'Jul 2022 - Dec 2022', location: 'Camp Pendleton, CA', role: 'Platoon Leader' },
          { period: 'Jan 2022 - Jun 2022', location: 'Fort Hood, TX', role: 'Assistant Operations Officer' }
        ]);

        setFitnessScores([
          { date: '2024-03-15', pushUps: 72, sitUps: 78, runTime: '13:20', score: 270 },
          { date: '2023-09-10', pushUps: 68, sitUps: 75, runTime: '13:45', score: 255 },
          { date: '2023-03-22', pushUps: 65, sitUps: 72, runTime: '14:10', score: 240 }
        ]);
      }
      else if (user.id === 2) { // Sarah Wilson
        setMedicalRecords([
          { date: '2024-02-20', type: 'Annual Physical', status: 'Completed', result: 'Passed' },
          { date: '2023-11-05', type: 'Vaccination', status: 'Completed', result: 'Administered' },
          { date: '2023-07-18', type: 'Dental Checkup', status: 'Completed', result: 'Passed' },
          { date: '2023-03-12', type: 'Vision Test', status: 'Completed', result: 'Passed with correction' }
        ]);

        setDeploymentHistory([
          { period: 'Feb 2023 - Aug 2023', location: 'Ramstein Air Base, Germany', role: 'Flight Commander' },
          { period: 'Jun 2022 - Jan 2023', location: 'Nellis AFB, NV', role: 'Operations Officer' },
          { period: 'Jan 2022 - May 2022', location: 'Lackland AFB, TX', role: 'Training Instructor' }
        ]);

        setFitnessScores([
          { date: '2024-02-10', pushUps: 65, sitUps: 80, runTime: '14:05', score: 265 },
          { date: '2023-08-15', pushUps: 62, sitUps: 78, runTime: '14:30', score: 255 },
          { date: '2023-02-22', pushUps: 60, sitUps: 75, runTime: '14:45', score: 245 }
        ]);
      }
      else if (user.id === 3) { // Michael Johnson
        setMedicalRecords([
          { date: '2024-03-05', type: 'Annual Physical', status: 'Completed', result: 'Passed' },
          { date: '2023-10-12', type: 'Vaccination', status: 'Completed', result: 'Administered' },
          { date: '2023-06-30', type: 'Dental Checkup', status: 'Completed', result: 'Needs follow-up' },
          { date: '2023-01-15', type: 'Vision Test', status: 'Completed', result: 'Passed' }
        ]);

        setDeploymentHistory([
          { period: 'Mar 2023 - Sep 2023', location: 'Camp Lejeune, NC', role: 'Squad Leader' },
          { period: 'Aug 2022 - Feb 2023', location: 'Okinawa, Japan', role: 'Infantry Squad Leader' },
          { period: 'Jan 2022 - Jul 2022', location: 'Camp Pendleton, CA', role: 'Rifleman' }
        ]);

        setFitnessScores([
          { date: '2024-01-20', pushUps: 85, sitUps: 90, runTime: '12:30', score: 290 },
          { date: '2023-07-15', pushUps: 82, sitUps: 88, runTime: '12:45', score: 285 },
          { date: '2023-01-10', pushUps: 80, sitUps: 85, runTime: '13:00', score: 280 }
        ]);
      }
    }
  }, [user]);

  return (
    <div className="dashboard-container">
      <h1>Logistics Command Personnel Dashboard</h1>

      <div className="dashboard-welcome">
        <img src="/images/logos/HQLC.png" alt="Logistics Command Logo" className="dashboard-logo" />
        <h2>Welcome, {personalInfo.name}</h2>
        <p>Here you can view and manage your comprehensive medical information, schedule appointments, track medications, and monitor your health status. Your well-being is our priority at the Nigerian Navy Logistics Command Medical Services.</p>
      </div>

      <div className="confidentiality-notice">
        <div className="notice-icon">⚠️</div>
        <div className="notice-content">
          <h3>CONFIDENTIALITY NOTICE</h3>
          <p>The information contained in this portal is STAFF IN CONFIDENCE. It contains personal and medical information that is protected under the Nigerian Data Protection Regulation and Military Health Information Privacy policies.</p>
          <p>Unauthorized access, use, or disclosure of this information is strictly prohibited and may result in disciplinary action and/or criminal prosecution.</p>
        </div>
      </div>

      <div className="dashboard-grid">
        {/* Personal Information Card */}
        <div className="dashboard-card">
          <div className="card-header">
            <h2>Personal Information</h2>
          </div>
          <div className="card-content">
            <div className="info-grid">
              <div className="info-section">
                <h3>Basic Information</h3>
                <div className="info-item">
                  <span className="info-label">Name:</span>
                  <span className="info-value">{personalInfo.name}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Rank:</span>
                  <span className="info-value">{personalInfo.rank}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Service Number:</span>
                  <span className="info-value">{personalInfo.serviceNumber}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Branch:</span>
                  <span className="info-value">{personalInfo.branch}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Division:</span>
                  <span className="info-value">{personalInfo.division}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>Personal Details</h3>
                <div className="info-item">
                  <span className="info-label">Date of Birth:</span>
                  <span className="info-value">{personalInfo.dateOfBirth}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Age:</span>
                  <span className="info-value">{personalInfo.age}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Sex:</span>
                  <span className="info-value">{personalInfo.sex}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Marital Status:</span>
                  <span className="info-value">{personalInfo.maritalStatus}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">State of Origin:</span>
                  <span className="info-value">{personalInfo.stateOfOrigin}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">LGA:</span>
                  <span className="info-value">{personalInfo.lga}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>Medical Information</h3>
                <div className="info-item">
                  <span className="info-label">Blood Type:</span>
                  <span className="info-value">{personalInfo.bloodType}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Genotype:</span>
                  <span className="info-value">{personalInfo.genotype}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Height:</span>
                  <span className="info-value">{personalInfo.height}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Weight:</span>
                  <span className="info-value">{personalInfo.weight}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>Contact Information</h3>
                <div className="info-item">
                  <span className="info-label">NIN:</span>
                  <span className="info-value">{personalInfo.nin}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Phone Number:</span>
                  <span className="info-value">{personalInfo.phoneNumber}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Address:</span>
                  <span className="info-value">{personalInfo.residentialAddress}</span>
                </div>
              </div>

              <div className="info-section">
                <h3>Next of Kin</h3>
                <div className="info-item">
                  <span className="info-label">Name:</span>
                  <span className="info-value">{personalInfo.nextOfKin}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Relationship:</span>
                  <span className="info-value">{personalInfo.nextOfKinRelationship}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Contact:</span>
                  <span className="info-value">{personalInfo.nextOfKinContact}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Medical Records Card */}
        <div className="dashboard-card">
          <div className="card-header">
            <h2>Medical Records</h2>
          </div>
          <div className="card-content">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Result</th>
                </tr>
              </thead>
              <tbody>
                {medicalRecords.map((record, index) => (
                  <tr key={index}>
                    <td>{record.date}</td>
                    <td>{record.type}</td>
                    <td>{record.status}</td>
                    <td>{record.result}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Deployment History Card */}
        <div className="dashboard-card">
          <div className="card-header">
            <h2>Deployment History</h2>
          </div>
          <div className="card-content">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Period</th>
                  <th>Location</th>
                  <th>Role</th>
                </tr>
              </thead>
              <tbody>
                {deploymentHistory.map((deployment, index) => (
                  <tr key={index}>
                    <td>{deployment.period}</td>
                    <td>{deployment.location}</td>
                    <td>{deployment.role}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Fitness Scores Card */}
        <div className="dashboard-card">
          <div className="card-header">
            <h2>Physical Fitness Scores</h2>
          </div>
          <div className="card-content">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Push-ups</th>
                  <th>Sit-ups</th>
                  <th>Run Time</th>
                  <th>Total Score</th>
                </tr>
              </thead>
              <tbody>
                {fitnessScores.map((score, index) => (
                  <tr key={index}>
                    <td>{score.date}</td>
                    <td>{score.pushUps}</td>
                    <td>{score.sitUps}</td>
                    <td>{score.runTime}</td>
                    <td>{score.score}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Medical Services Section */}
      <div className="dashboard-sections">
        {/* Consultation Requests Section */}
        <div className="collapsible-section">
          <div
            className={`section-header ${expandedSections.consultationRequests ? 'expanded' : ''}`}
            onClick={() => toggleSection('consultationRequests')}
          >
            <h2>Medical Consultation Requests</h2>
            <span className="toggle-icon">{expandedSections.consultationRequests ? '−' : '+'}</span>
          </div>

          {expandedSections.consultationRequests && (
            <div className="section-content">
              <ConsultationRequest user={user} />
            </div>
          )}
        </div>

        {/* Appointments Section */}
        <div className="collapsible-section">
          <div
            className={`section-header ${expandedSections.appointments ? 'expanded' : ''}`}
            onClick={() => toggleSection('appointments')}
          >
            <h2>Appointments</h2>
            <span className="toggle-icon">{expandedSections.appointments ? '−' : '+'}</span>
          </div>

          {expandedSections.appointments && (
            <div className="section-content">
              <AppointmentScheduler user={user} />
            </div>
          )}
        </div>

        {/* Medications Section */}
        <div className="collapsible-section">
          <div
            className={`section-header ${expandedSections.medications ? 'expanded' : ''}`}
            onClick={() => toggleSection('medications')}
          >
            <h2>Medications</h2>
            <span className="toggle-icon">{expandedSections.medications ? '−' : '+'}</span>
          </div>

          {expandedSections.medications && (
            <div className="section-content">
              <MedicationTracker user={user} />
            </div>
          )}
        </div>

        {/* Emergency Access Section */}
        <div className="emergency-access-section">
          <h2>Emergency Medical Access</h2>
          <p>If you are unable to initiate a consultation request or are experiencing a medical emergency:</p>
          <div className="emergency-options">
            <div className="emergency-option">
              <h3>Call Emergency Services</h3>
              <p>For immediate medical emergencies, call:</p>
              <div className="emergency-contact">112</div>
              <p>or the Logistics Command Medical Center:</p>
              <div className="emergency-contact">08012345678</div>
            </div>
            <div className="emergency-option">
              <h3>Visit Medical Center</h3>
              <p>You can visit either medical facility directly:</p>
              <ul>
                <li>Naval Medical Centre Oghara</li>
                <li>HQ Logistics Command Sick Bay</li>
              </ul>
              <p>Medical staff will assist you immediately.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;