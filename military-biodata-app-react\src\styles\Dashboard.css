.dashboard-container {
  padding: 1.5rem;
  font-family: 'Segoe UI', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-container h1 {
  color: #003366;
  margin-bottom: 0.5rem;
  text-align: center;
  font-size: 1.8rem;
}

.dashboard-welcome {
  background: linear-gradient(135deg, #003366 0%, #004080 100%);
  color: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.confidentiality-notice {
  background-color: #fff4e5;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: flex-start;
  border: 1px solid #ff9800;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.notice-icon {
  font-size: 2rem;
  margin-right: 1rem;
  color: #ff9800;
}

.notice-content h3 {
  color: #e65100;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 700;
}

.notice-content p {
  color: #333;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.dashboard-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.dashboard-logo:hover {
  transform: scale(1.05);
}

.dashboard-welcome::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.05) 10px,
    rgba(255, 255, 255, 0.1) 10px,
    rgba(255, 255, 255, 0.1) 20px
  );
  z-index: 0;
}

.dashboard-welcome h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  position: relative;
  z-index: 1;
}

.dashboard-welcome p {
  font-size: 1.1rem;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #003366;
  color: white;
  padding: 1rem;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card-header h2 {
  margin: 0;
  font-size: 1.3rem;
}

.card-content {
  padding: 1.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.info-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-section h3 {
  color: #003366;
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.info-item {
  display: flex;
  margin-bottom: 0.75rem;
}

.info-label {
  font-weight: bold;
  color: #555;
  font-size: 0.9rem;
  width: 140px;
  flex-shrink: 0;
}

.info-value {
  font-size: 0.95rem;
  color: #333;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #555;
}

.data-table tr:hover {
  background-color: #f9f9f9;
}

/* Status colors */
.completed {
  color: #27ae60;
  font-weight: bold;
}

.pending {
  color: #e67e22;
  font-weight: bold;
}

/* Collapsible Sections */
.dashboard-sections {
  margin-top: 2rem;
}

.collapsible-section {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-header {
  background-color: #003366;
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.section-header:hover {
  background-color: #004080;
}

.section-header h2 {
  margin: 0;
  font-size: 1.3rem;
}

.toggle-icon {
  font-size: 1.5rem;
  font-weight: bold;
}

.section-header.expanded {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-content {
  background-color: white;
  padding: 0;
}

/* Emergency Access Section */
.emergency-access-section {
  background-color: #fff4e5;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
  border: 1px solid #ff9800;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.emergency-access-section h2 {
  color: #e65100;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.emergency-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 1rem;
}

.emergency-option {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.emergency-option h3 {
  color: #003366;
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.emergency-option p {
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.emergency-contact {
  background-color: #f0f7ff;
  color: #003366;
  font-size: 1.2rem;
  font-weight: bold;
  padding: 0.5rem;
  margin: 0.5rem 0;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #cce5ff;
}

.emergency-option ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.emergency-option li {
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .emergency-options {
    grid-template-columns: 1fr;
  }
}

