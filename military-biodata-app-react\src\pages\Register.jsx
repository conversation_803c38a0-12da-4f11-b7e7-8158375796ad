import React, { useState } from 'react';
import api from '../api';

const Register = () => {
  const [form, setForm] = useState({ serviceNo: '', email: '', password: '', role: 'personnel' });

  const register = () => {
    api.post('/auth/register', form)
      .then(/**
 * Displays an alert message to the user upon successful registration.
 * 
 * This function is called when the registration process is completed,
 * informing the user that they can now log in.
 */
() => alert('Registered! Now login.'))
      .catch(/**
 * Displays an alert message to the user when registration fails.
 * 
 * This function is called when there is an error during the 
 * registration process, informing the user that the registration 
 * attempt was unsuccessful.
 * 
 * @param {Error} err - The error object containing details about 
 * the registration failure.
 */
err => alert('Registration failed'));
  };

  return (
    <div className="auth-form">
      <h2>📝 Register</h2>
      <input value={form.serviceNo} onChange={/**
 * Updates the form state with the new service number.
 * 
 * @param {Event} e - The event object from the input change.
 * This function takes the event from an input field and updates
 * the 'serviceNo' property in the form state with the value
 * from the input field.
 */
e => setForm({ ...form, serviceNo: e.target.value })} placeholder="Service Number" />
      <input value={form.email} onChange={/**
 * Updates the form state with the new email value.
 * 
 * This function is triggered when the user inputs a new email 
 * address in the corresponding input field. It updates the 'email' 
 * property in the form state with the value from the event target.
 * 
 * @param {Event} e - The event object from the input change.
 */
e => setForm({ ...form, email: e.target.value })} placeholder="Email" />
      <input type="password" value={form.password} onChange={/**
 * Updates the form state with the new password value.
 * 
 * This function is triggered when the user inputs a new password 
 * in the corresponding input field. It updates the 'password' 
 * property in the form state with the value from the event target.
 * 
 * @param {Event} e - The event object from the input change.
 */
e => setForm({ ...form, password: e.target.value })} placeholder="Password" />
      <select value={form.role} onChange={/**
 * Updates the form state with the new role value.
 * 
 * @param {Event} e - The event object from the input change.
 * This function extracts the value from the event target and 
 * updates the 'role' property in the form state.
 */
/**
 * Updates the form state with the new role value.
 * 
 * @param {Event} e - The event object from the input change.
 * This function extracts the value from the event target and 
 * updates the 'role' property in the form state.
 */
/**
 * Updates the form state with the new role value.
 * 
 * This function is triggered when the user selects a new role 
 * from an input field. It updates the 'role' property in the 
 * form state with the value from the event target.
 * 
 * @param {Event} e - The event object from the input change.
 */
/**
 * Updates the form state with the new role value.
 * 
 * This function is triggered when the user selects a new role 
 * from an input field. It updates the 'role' property in the 
 * form state with the value from the event target.
 * 
 * @param {Event} e - The event object from the input change.
 */
e => setForm({ ...form, role: e.target.value })}>
        <option value="personnel">Personnel</option>
        <option value="nurse">Nurse</option>
        <option value="doctor">Doctor</option>
      </select>
      <button onClick={register}>Register</button>
    </div>
  );
};

export default Register;
