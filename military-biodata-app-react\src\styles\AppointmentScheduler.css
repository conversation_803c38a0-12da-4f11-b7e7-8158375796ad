.appointment-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.appointment-header h2 {
  margin: 0;
  color: #003366;
  font-size: 1.5rem;
}

.new-appointment-btn {
  background-color: #003366;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.new-appointment-btn:hover {
  background-color: #002244;
}

.appointment-form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eee;
}

.appointment-form-container h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #003366;
  font-size: 1.2rem;
}

.appointment-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #555;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #003366;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.1);
}

.submit-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  grid-column: 1 / -1;
  margin-top: 1rem;
}

.submit-btn:hover {
  background-color: #218838;
}

.appointments-list h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #003366;
  font-size: 1.2rem;
}

.appointment-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.appointment-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.appointment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.appointment-card-header {
  background-color: #003366;
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.appointment-type {
  font-weight: 600;
  font-size: 1rem;
}

.appointment-status {
  background-color: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
}

.appointment-card-body {
  padding: 1rem;
}

.appointment-detail {
  margin-bottom: 0.5rem;
  display: flex;
}

.detail-label {
  font-weight: 600;
  color: #555;
  width: 80px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
}

.appointment-card-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
}

.reschedule-btn,
.cancel-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
  border: none;
}

.reschedule-btn {
  background-color: #f8f9fa;
  color: #003366;
  border: 1px solid #003366;
}

.reschedule-btn:hover {
  background-color: #e9ecef;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.cancel-btn:hover {
  background-color: #e9ecef;
}

.no-appointments {
  text-align: center;
  color: #666;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .appointment-form {
    grid-template-columns: 1fr;
  }
  
  .appointment-cards {
    grid-template-columns: 1fr;
  }
}
