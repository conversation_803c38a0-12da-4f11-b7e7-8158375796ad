import React, { useState } from 'react';
import { FaBookMedical, FaRunning, FaHeartbeat, FaBrain, FaComment } from 'react-icons/fa';
import '../styles/MedBlog.css';

const MedBlog = () => {
  const [entry, setEntry] = useState('');
  const [comments, setComments] = useState([
    { text: "Great article on hydration! I've started carrying a water bottle during duty.", date: new Date(2023, 5, 15) },
    { text: "The sleep tips really helped improve my rest during deployment.", date: new Date(2023, 5, 10) }
  ]);

  const submitComment = () => {
    if (entry.trim()) {
      setComments([...comments, { text: entry, date: new Date() }]);
      setEntry('');
    }
  };

  return (
    <div className="med-blog">
      <div className="blog-header">
        <h1><FaHeartbeat /> NavMed Pulse</h1>
        <p>Your source for naval health insights and wellness strategies</p>
      </div>
      
      <div className="featured-article">
        <div className="article-image">
          <img src="/images/deployment-fitness.jpg" alt="Naval personnel exercising" 
               onError={(e) => e.target.src = 'https://via.placeholder.com/800x400?text=Naval+Fitness'} />
        </div>
        <div className="article-content">
          <span className="article-tag">FEATURED</span>
          <h2>Combat-Ready Fitness: Maintaining Peak Performance at Sea</h2>
          <p className="article-meta">By Commander James Wilson, MD • June 15, 2023</p>
          <p className="article-excerpt">
            Maintaining physical readiness during extended deployments presents unique challenges. 
            This comprehensive guide offers practical strategies for staying fit in confined spaces, 
            nutritional advice for maritime operations, and mental resilience techniques.
          </p>
          <button className="read-more">Read Full Article</button>
        </div>
      </div>

      <div className="blog-grid">
        <div className="blog-card">
          <FaRunning className="card-icon" />
          <h3>5 High-Intensity Workouts for Small Spaces</h3>
          <p>Effective exercise routines designed specifically for shipboard environments.</p>
          <span className="read-time">5 min read</span>
        </div>
        
        <div className="blog-card">
          <FaHeartbeat className="card-icon" />
          <h3>Understanding Your Blood Pressure Readings</h3>
          <p>What those numbers really mean and how to maintain healthy levels during service.</p>
          <span className="read-time">7 min read</span>
        </div>
        
        <div className="blog-card">
          <FaBrain className="card-icon" />
          <h3>Combat Stress Management Techniques</h3>
          <p>Evidence-based strategies to maintain mental resilience during high-pressure operations.</p>
          <span className="read-time">8 min read</span>
        </div>
        
        <div className="blog-card">
          <FaBookMedical className="card-icon" />
          <h3>Preventive Health: Your Annual Checkup Guide</h3>
          <p>What to expect and how to prepare for your mandatory annual medical assessment.</p>
          <span className="read-time">6 min read</span>
        </div>
      </div>

      <div className="contribute-banner">
        <h3>Share Your Expertise</h3>
        <p>Are you a medical professional with insights to share? Submit your article to help fellow naval personnel.</p>
        <button>Contribute Now</button>
      </div>

      <section className="comments-section">
        <h3><FaComment /> Discussion</h3>
        <div className="comment-form">
          <textarea 
            value={entry} 
            onChange={(e) => setEntry(e.target.value)} 
            placeholder="Share your thoughts or questions..."
          />
          <button onClick={submitComment}>Post Comment</button>
        </div>
        <div className="comments-list">
          {comments.map((c, i) => (
            <div key={i} className="comment">
              <div className="comment-header">
                <img 
                  src={localStorage.getItem('userAvatar') || '/default-avatar.png'} 
                  alt="User" 
                  className="comment-avatar" 
                  onError={(e) => e.target.src = 'https://via.placeholder.com/40'} 
                />
                <div>
                  <span className="comment-author">{localStorage.getItem('userName') || 'Anonymous User'}</span>
                  <span className="comment-date">{c.date.toLocaleDateString()}</span>
                </div>
              </div>
              <p className="comment-text">{c.text}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default MedBlog;


