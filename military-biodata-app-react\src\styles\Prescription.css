.prescription-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.prescription-container h2 {
  color: #003366;
  margin-bottom: 1rem;
  border-bottom: 2px solid #003366;
  padding-bottom: 0.5rem;
}

.doctor-info,
.patient-info {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.doctor-info {
  padding: 0.5rem;
  background-color: #f0f7ff;
  border-left: 4px solid #003366;
  border-radius: 4px;
}

.patient-info {
  padding: 0.5rem;
  background-color: #f5f5f5;
  border-left: 4px solid #666;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.prescription-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1.5rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #003366;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 51, 102, 0.2);
}

.error-message {
  color: #d32f2f;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.medications-section {
  margin-top: 1rem;
}

.medications-section h3 {
  color: #003366;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.medication-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eee;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.medication-header h4 {
  color: #003366;
  margin: 0;
}

.remove-medication-btn {
  background-color: transparent;
  color: #d32f2f;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-medication-btn:hover {
  background-color: rgba(211, 47, 47, 0.1);
}

.add-medication-btn {
  background-color: transparent;
  color: #003366;
  border: 1px dashed #003366;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
  margin-bottom: 1.5rem;
  transition: all 0.2s;
}

.add-medication-btn:hover {
  background-color: rgba(0, 51, 102, 0.05);
}

.form-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #003366;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #004080;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.previous-prescriptions-section {
  margin-top: 2rem;
  border-top: 1px solid #eee;
  padding-top: 1.5rem;
}

.toggle-prescriptions-button {
  background-color: transparent;
  color: #003366;
  border: 1px solid #003366;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-prescriptions-button:hover {
  background-color: #f0f7ff;
}

.previous-prescriptions {
  margin-top: 1.5rem;
}

.previous-prescriptions h3 {
  color: #003366;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.prescriptions-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.prescription-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #eee;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.prescription-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.prescription-body {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prescription-medications ul {
  margin-top: 0.5rem;
  padding-left: 1.5rem;
}

.prescription-medications li {
  margin-bottom: 0.5rem;
}

.med-instructions {
  font-style: italic;
  margin-top: 0.25rem;
  color: #666;
}

.unauthorized-message {
  background-color: #fff4e5;
  border-left: 4px solid #ff9800;
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 4px;
}

.unauthorized-message p {
  margin: 0.5rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .prescription-header {
    flex-direction: column;
    gap: 0.5rem;
  }
}
