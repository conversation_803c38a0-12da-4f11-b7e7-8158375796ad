.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: #333;
}

.about-header {
  text-align: center;
  margin-bottom: 3rem;
}

.about-header h1 {
  font-size: 2.5rem;
  color: #003366;
  margin-bottom: 0.5rem;
}

.about-header p {
  font-size: 1.2rem;
  color: #666;
}

.about-section {
  margin-bottom: 4rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  background-color: #003366;
  color: white;
  padding: 1rem 2rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.section-content {
  padding: 2rem;
}

/* Command Info Section */
.command-info .section-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.command-logo {
  flex: 0 0 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.command-main-logo {
  width: 200px;
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.command-main-logo:hover {
  transform: scale(1.05);
}

.command-description {
  flex: 1;
}

.command-description p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Commander Message Section */
.commander-message .section-content {
  display: flex;
  gap: 2rem;
}

.commander-photo {
  flex: 0 0 200px;
}

.foc-photo-container {
  width: 200px;
  height: 250px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 2px solid #003366;
}

.foc-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.foc-photo-container:hover .foc-photo {
  transform: scale(1.05);
}

.foc-photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 51, 102, 0.9), transparent);
  padding: 1rem;
  color: white;
  text-align: center;
}

.foc-photo-overlay p {
  margin: 0;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.foc-title {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 0.25rem !important;
}

.commander-message-text {
  flex: 1;
}

.commander-message-text p {
  margin-bottom: 1rem;
  line-height: 1.6;
  font-style: italic;
}

.commander-signature {
  text-align: right;
  font-style: normal;
  font-weight: bold;
  margin-top: 2rem;
  color: #003366;
  border-top: 1px solid rgba(0, 51, 102, 0.2);
  padding-top: 1rem;
}

/* Medical Facilities Section */
.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.facility-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.facility-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.facility-image {
  margin-bottom: 1rem;
}

.facility-image-container {
  height: 150px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 2px solid #003366;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.facility-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  padding: 1rem;
  transition: transform 0.3s ease;
}

.facility-image-container:hover .facility-img {
  transform: scale(1.05);
}

.facility-card h3 {
  color: #003366;
  margin-bottom: 0.5rem;
}

.facility-features {
  margin-top: 1rem;
  padding-left: 1.5rem;
}

.facility-features li {
  margin-bottom: 0.5rem;
}

/* Medical Services Section */
.services-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.service-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.service-item:hover {
  transform: translateY(-5px);
}

.service-item h3 {
  color: #003366;
  margin-bottom: 0.5rem;
}

/* Unit Logos Section */
.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 2rem;
  justify-items: center;
}

.unit-logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s ease;
}

.unit-logo-item:hover {
  transform: translateY(-5px);
}

.unit-logo {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.unit-logo-item:hover .unit-logo {
  transform: scale(1.05);
}

.unit-logo-item p {
  font-weight: bold;
  color: #003366;
  margin-top: 0.5rem;
}

/* Contact Info Section */
.contact-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.contact-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.contact-item h3 {
  color: #003366;
  margin-bottom: 0.5rem;
}

.contact-item p {
  margin-bottom: 0.5rem;
}

.contact-item a {
  color: #003366;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .command-info .section-content,
  .commander-message .section-content {
    flex-direction: column;
    align-items: center;
  }

  .command-logo,
  .commander-photo {
    margin-bottom: 1.5rem;
  }

  .facilities-grid,
  .services-list,
  .contact-details {
    grid-template-columns: 1fr;
  }

  .logos-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
