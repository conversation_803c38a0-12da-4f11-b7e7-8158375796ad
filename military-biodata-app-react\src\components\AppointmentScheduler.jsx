import React, { useState, useEffect } from 'react';
import '../styles/AppointmentScheduler.css';

const AppointmentScheduler = ({ user }) => {
  // State for appointments
  const [appointments, setAppointments] = useState([]);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedFacility, setSelectedFacility] = useState('');
  const [notes, setNotes] = useState('');
  const [showForm, setShowForm] = useState(false);
  
  // Appointment types
  const appointmentTypes = [
    'Annual Physical',
    'Dental Checkup',
    'Vision Test',
    'Vaccination',
    'Specialist Consultation',
    'Follow-up Visit',
    'Laboratory Test',
    'Mental Health Consultation'
  ];
  
  // Medical facilities
  const medicalFacilities = [
    'Logistics Command Medical Center',
    'Regional Clinic Alpha',
    'Regional Clinic Bravo',
    'Mobile Medical Unit 1',
    'Mobile Medical Unit 2'
  ];
  
  // Generate available time slots
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 8; hour < 17; hour++) {
      slots.push(`${hour}:00`);
      slots.push(`${hour}:30`);
    }
    return slots;
  };
  
  // Load user-specific appointments
  useEffect(() => {
    if (user) {
      // In a real app, this would be an API call
      // For demo purposes, we'll use mock data based on user ID
      let userAppointments = [];
      
      if (user.id === 1) { // John Doe
        userAppointments = [
          {
            id: 1,
            date: '2025-05-15',
            time: '10:00',
            type: 'Annual Physical',
            facility: 'Logistics Command Medical Center',
            status: 'Scheduled',
            notes: 'Regular annual checkup'
          },
          {
            id: 2,
            date: '2025-06-10',
            time: '14:30',
            type: 'Dental Checkup',
            facility: 'Regional Clinic Alpha',
            status: 'Scheduled',
            notes: 'Follow-up on previous treatment'
          }
        ];
      } else if (user.id === 2) { // Sarah Wilson
        userAppointments = [
          {
            id: 3,
            date: '2025-05-20',
            time: '09:00',
            type: 'Specialist Consultation',
            facility: 'Logistics Command Medical Center',
            status: 'Scheduled',
            notes: 'Orthopedic consultation'
          }
        ];
      } else if (user.id === 3) { // Michael Johnson
        userAppointments = [
          {
            id: 4,
            date: '2025-05-18',
            time: '11:30',
            type: 'Vaccination',
            facility: 'Mobile Medical Unit 1',
            status: 'Scheduled',
            notes: 'Annual flu vaccination'
          },
          {
            id: 5,
            date: '2025-06-05',
            time: '13:00',
            type: 'Laboratory Test',
            facility: 'Regional Clinic Bravo',
            status: 'Scheduled',
            notes: 'Routine blood work'
          }
        ];
      }
      
      setAppointments(userAppointments);
      setAvailableSlots(generateTimeSlots());
    }
  }, [user]);
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Create new appointment
    const newAppointment = {
      id: appointments.length + 1,
      date: selectedDate,
      time: selectedTime,
      type: selectedType,
      facility: selectedFacility,
      status: 'Scheduled',
      notes: notes
    };
    
    // Add to appointments list
    setAppointments([...appointments, newAppointment]);
    
    // Reset form
    setSelectedDate('');
    setSelectedTime('');
    setSelectedType('');
    setSelectedFacility('');
    setNotes('');
    setShowForm(false);
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Get today's date in YYYY-MM-DD format for min date in date picker
  const today = new Date().toISOString().split('T')[0];
  
  return (
    <div className="appointment-container">
      <div className="appointment-header">
        <h2>Medical Appointments</h2>
        <button 
          className="new-appointment-btn"
          onClick={() => setShowForm(!showForm)}
        >
          {showForm ? 'Cancel' : 'Schedule New Appointment'}
        </button>
      </div>
      
      {showForm && (
        <div className="appointment-form-container">
          <h3>Schedule New Appointment</h3>
          <form onSubmit={handleSubmit} className="appointment-form">
            <div className="form-group">
              <label htmlFor="appointment-date">Date</label>
              <input
                type="date"
                id="appointment-date"
                min={today}
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="appointment-time">Time</label>
              <select
                id="appointment-time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                required
              >
                <option value="">Select a time</option>
                {availableSlots.map((slot, index) => (
                  <option key={index} value={slot}>{slot}</option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="appointment-type">Type</label>
              <select
                id="appointment-type"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                required
              >
                <option value="">Select appointment type</option>
                {appointmentTypes.map((type, index) => (
                  <option key={index} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="appointment-facility">Facility</label>
              <select
                id="appointment-facility"
                value={selectedFacility}
                onChange={(e) => setSelectedFacility(e.target.value)}
                required
              >
                <option value="">Select medical facility</option>
                {medicalFacilities.map((facility, index) => (
                  <option key={index} value={facility}>{facility}</option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="appointment-notes">Notes</label>
              <textarea
                id="appointment-notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any additional information"
              />
            </div>
            
            <button type="submit" className="submit-btn">Schedule Appointment</button>
          </form>
        </div>
      )}
      
      <div className="appointments-list">
        <h3>Upcoming Appointments</h3>
        {appointments.length > 0 ? (
          <div className="appointment-cards">
            {appointments.map((appointment) => (
              <div key={appointment.id} className="appointment-card">
                <div className="appointment-card-header">
                  <span className="appointment-type">{appointment.type}</span>
                  <span className="appointment-status">{appointment.status}</span>
                </div>
                <div className="appointment-card-body">
                  <div className="appointment-detail">
                    <span className="detail-label">Date:</span>
                    <span className="detail-value">{formatDate(appointment.date)}</span>
                  </div>
                  <div className="appointment-detail">
                    <span className="detail-label">Time:</span>
                    <span className="detail-value">{appointment.time}</span>
                  </div>
                  <div className="appointment-detail">
                    <span className="detail-label">Facility:</span>
                    <span className="detail-value">{appointment.facility}</span>
                  </div>
                  {appointment.notes && (
                    <div className="appointment-detail">
                      <span className="detail-label">Notes:</span>
                      <span className="detail-value">{appointment.notes}</span>
                    </div>
                  )}
                </div>
                <div className="appointment-card-footer">
                  <button className="reschedule-btn">Reschedule</button>
                  <button className="cancel-btn">Cancel</button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="no-appointments">No upcoming appointments scheduled.</p>
        )}
      </div>
    </div>
  );
};

export default AppointmentScheduler;
